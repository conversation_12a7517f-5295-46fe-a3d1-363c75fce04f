# Generated by Django 4.2 on 2025-05-21 02:11

from django.conf import settings
from django.db import migrations, models
import django.db.models.deletion


class Migration(migrations.Migration):

    dependencies = [
        ('cls_backend', '0013_package_limit_request'),
    ]

    operations = [
        migrations.RemoveField(
            model_name='package',
            name='limit_request',
        ),
        migrations.RemoveField(
            model_name='thamquyennoidung',
            name='van_de_ban_hanh',
        ),
        migrations.AlterField(
            model_name='document',
            name='loai_van_ban',
            field=models.CharField(blank=True, choices=[('Quyết định', 'Quyết định'), ('Thông tư', 'Thông tư'), ('<PERSON><PERSON><PERSON> quyết', '<PERSON><PERSON><PERSON> quyết'), ('<PERSON><PERSON><PERSON> định', '<PERSON><PERSON><PERSON> định'), ('Thông tư liên tịch', 'Thông tư liên tịch'), ('<PERSON><PERSON><PERSON>', '<PERSON><PERSON><PERSON>'), ('<PERSON><PERSON><PERSON> bả<PERSON> hợ<PERSON> nhấ<PERSON>', '<PERSON><PERSON><PERSON> bản hợp nhấ<PERSON>'), ('<PERSON><PERSON><PERSON> lệnh', '<PERSON><PERSON><PERSON> l<PERSON>nh'), ('<PERSON>ông văn', '<PERSON>ông văn'), ('Bộ luật', 'Bộ luật'), ('Nghị quyết liên tịch', 'Nghị quyết liên tịch'), ('Chỉ thị', 'Chỉ thị'), ('Văn bản kh<PERSON>c', 'Văn bản khác'), ('Lệnh', 'Lệnh'), ('Hiến pháp', 'Hiến pháp'), ('Văn bản liên quan', 'Văn bản liên quan'), ('Thông báo', 'Thông báo'), ('Chương trình', 'Chương trình'), ('Sắc lệnh', 'Sắc lệnh'), ('Thông tư liên bộ', 'Thông tư liên bộ'), ('Hiệp định', 'Hiệp định'), ('Sắc luật', 'Sắc luật'), ('Báo cáo', 'Báo cáo')], max_length=50, null=True),
        ),
        migrations.AlterField(
            model_name='document',
            name='title',
            field=models.CharField(blank=True, max_length=5000, null=True),
        ),
        migrations.AlterField(
            model_name='document',
            name='trich_yeu',
            field=models.CharField(blank=True, max_length=1000, null=True),
        ),
        migrations.AlterField(
            model_name='lawclause',
            name='law_title',
            field=models.CharField(blank=True, max_length=2000, null=True),
        ),
        migrations.AlterField(
            model_name='lawclause',
            name='position',
            field=models.CharField(blank=True, max_length=100, null=True),
        ),
        migrations.AlterField(
            model_name='lawclause',
            name='reason',
            field=models.CharField(blank=True, max_length=200, null=True),
        ),
        migrations.AlterField(
            model_name='lawclause',
            name='title',
            field=models.CharField(blank=True, max_length=2000, null=True),
        ),
        migrations.AlterField(
            model_name='thamquyennoidung',
            name='co_quan_co_tham_quyen',
            field=models.CharField(blank=True, max_length=1000, null=True),
        ),
        migrations.AlterField(
            model_name='thamquyennoidung',
            name='ket_qua',
            field=models.CharField(blank=True, max_length=1000, null=True),
        ),
        migrations.AlterField(
            model_name='thamquyennoidung',
            name='ly_do',
            field=models.CharField(blank=True, max_length=1000, null=True),
        ),
        migrations.CreateModel(
            name='Report',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
                ('deleted_at', models.DateTimeField(blank=True, null=True)),
                ('content', models.TextField()),
                ('report_type', models.CharField(choices=[('bug', 'Bug Report'), ('ui', 'UI Report')], max_length=10)),
                ('status', models.IntegerField(choices=[(0, 'Pending'), (1, 'Resolved')], default=0)),
                ('attachments', models.FileField(blank=True, null=True, upload_to='reports/%Y/%m/%d')),
                ('user', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to=settings.AUTH_USER_MODEL)),
            ],
            options={
                'abstract': False,
            },
        ),
    ]
