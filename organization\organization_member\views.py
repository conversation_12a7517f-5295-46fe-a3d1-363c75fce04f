from cls_backend.models import User
from organization.constants import *
from organization.serializers import OrganizationMemberSerializer
from ..models import Organization, OrganizationMember
from rest_framework import viewsets, status
from rest_framework.response import Response
from rest_framework.permissions import IsAuthenticated

class OrganizationMemberViewSet(viewsets.ModelViewSet):
    permission_classes = [IsAuthenticated]
    queryset = OrganizationMember.objects.all()
    serializer_class = OrganizationMemberSerializer
    
    def create(self, request, *args, **kwargs):
        organization_id = request.data.get("organization_id")
        user_ids_raw = request.data.get("user_ids", "")
        user_ids = [uid.strip() for uid in user_ids_raw.split(",") if uid.strip()]
        
        organization = Organization.objects.get(id=organization_id)
        users = User.objects.filter(id__in=user_ids)
        user_map = {str(u.id): u for u in users}
        
        members = OrganizationMember.objects.filter(user_id__in=user_ids).values("user_id", "role")
        
        admin_user_ids = [str(m["user_id"]) for m in members if m["role"] == ADMIN]
        existing_user_ids = [str(m["user_id"]) for m in members if m["role"] != ADMIN]

        valid_user_ids = [uid for uid in user_ids if uid not in admin_user_ids]

        OrganizationMember.objects.filter(user_id__in=existing_user_ids).update(organization=organization)

        new_members = [
            OrganizationMember(
                user=user_map[uid],
                organization=organization,
                role=MEMBER
            )
            for uid in valid_user_ids if uid not in existing_user_ids
        ]
        OrganizationMember.objects.bulk_create(new_members)
        
        return Response({
            "updated": list(existing_user_ids),
            "created": [uid for uid in valid_user_ids if uid not in existing_user_ids],
            "skipped_admins": list(admin_user_ids)
        }, status=status.HTTP_201_CREATED)
                
            
