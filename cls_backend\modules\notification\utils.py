from asgiref.sync import async_to_sync
from channels.layers import get_channel_layer
import logging


logger = logging.getLogger("cls")


def send_notifications(user_id, data, event, percentage: int=0):
    logger.debug([data, event])
    channel_layer = get_channel_layer()
    async_to_sync(channel_layer.group_send)(
        str(user_id),  # Group Name, Should always be string
        {
            "type": "notify",
            "data": data,
            "event": event,
            "percentage": percentage
        },
    )
