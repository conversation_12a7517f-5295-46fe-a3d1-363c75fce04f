from datetime import timedelta
from dateutil.relativedelta import relativedelta
from django.db.models import Sum, Count, Avg, Value, FloatField
from django.db.models.functions import Coalesce, TruncDay
from django.utils.timezone import now
from rest_framework import status
from rest_framework.permissions import IsAuthenticated, IsAdminUser
from rest_framework.response import Response
from rest_framework.views import APIView
from cls_backend.models import Document, DocumentCompare, LegalSearchHistory, User, DocumentAuthority
from django.utils.decorators import method_decorator
from django.views.decorators.cache import cache_page
from django.core.cache import cache
from collections import defaultdict


class ActivityUserSummaryView(APIView):
    permission_classes = [IsAuthenticated]

    def get_ocr_summary(self, user):
        ocr_document_count = (Document.objects.filter(created_by=user, deleted_at=None, is_convert=True).count())
        ocr_summary = {
            "docx_size_mb": 0,
            "file_count": ocr_document_count
        }

        return ocr_summary

    def get_document_summary(self, user):
        counts = (
            DocumentCompare.objects
                .filter(document__created_by=user.id, deleted_at=None)
                .count()
        )
        document_summary = {
            "file_count": counts
        }
        return document_summary

    def get_legal_search_summary(self, user):
        query_set = LegalSearchHistory.objects.filter(deleted_time__isnull=True, requested_by=user)
        legal_search_summary = query_set.values('requested_by') \
            .annotate(response_time_avg=Coalesce(Avg('response_time'), 0.0), num_requests=Count('requested_by'))
        if len(legal_search_summary) != 0:
            legal_search_summary = legal_search_summary[0]
            legal_search_summary.pop('requested_by')
        else:
            legal_search_summary = {
                "response_time_avg": 0.0,
                "num_requests": 0,
            }
        return legal_search_summary

    def get(self, request, *args, **kwargs):
        user = request.user
        ocr_summary = self.get_ocr_summary(user)
        document_summary = self.get_document_summary(user)
        legal_search_summary = self.get_legal_search_summary(user)
        data = {
            "ocr": ocr_summary,
            "document": document_summary,
            "legal_search": legal_search_summary,
        }
        return Response(data, status=status.HTTP_200_OK)
class OCRSummaryViewUser(APIView):
    permission_classes = [IsAuthenticated]

    def get_ocr_summary(self, user):
        ocr_document_count = (Document.objects.filter(created_by=user, deleted_at=None, is_convert=True).count())
        ocr_summary = {
            "docx_size_mb": 0,
            "file_count": ocr_document_count
        }

        return ocr_summary
    def get(self, request, *args, **kwargs):
        user = request.user
        ocr_summary = self.get_ocr_summary(user)
        return Response({"ocr": ocr_summary}, status=status.HTTP_200_OK)

class DocumentSummaryViewUser(APIView):
    permission_classes = [IsAuthenticated]
    def get_document_summary(self, user):
            counts = (
                DocumentCompare.objects
                    .filter(document__created_by=user.id, deleted_at=None)
                    .count()
            )
            document_summary = {
                "file_count": counts
            }
            return document_summary     
           
    def get(self, request, *args, **kwargs):
        user = request.user
        document_summary = self.get_document_summary(user)
        return Response({"document": document_summary}, status=status.HTTP_200_OK)

class LegalSearchSummaryViewUser(APIView):
    permission_classes = [IsAuthenticated]
    def get_legal_search_summary(self, user):
        query_set = LegalSearchHistory.objects.filter(deleted_time__isnull=True, requested_by=user)
        legal_search_summary = query_set.values('requested_by') \
            .annotate(response_time_avg=Coalesce(Avg('response_time'), 0.0), num_requests=Count('requested_by'))
        if len(legal_search_summary) != 0:
            legal_search_summary = legal_search_summary[0]
            legal_search_summary.pop('requested_by')
        else:
            legal_search_summary = {
                "response_time_avg": 0.0,
                "num_requests": 0,
            }
        return legal_search_summary
    def get(self, request, *args, **kwargs):
        user = request.user
        legal_search_summary = self.get_legal_search_summary(user)
        return Response({"legal_search": legal_search_summary}, status=status.HTTP_200_OK)


class ActivityUserByDayView(APIView):
    permission_classes = [IsAuthenticated]

    def split_data_by_day(self, data_by_day):
        data = {}
        for summary in data_by_day:
            day = summary['day']
            day_str = day.strftime("%d/%m/%Y")
            summary.pop('day')
            data[day_str] = summary
        return data

    def get_ocr_summary_by_day(self, user, days):
        current_day = now().replace(hour=0, minute=0, second=0, microsecond=0)
        ocr_by_day = Document.objects.filter(deleted_at=None, created_by=user, is_convert=True,
                                             created_at__gte=current_day + timedelta(days=-days)) \
            .annotate(day=TruncDay('created_at')).values('day') \
            .annotate(docx_size_mb=Value(0), file_count=Count('created_by'))
        data = self.split_data_by_day(ocr_by_day)
        return data

    def get_document_summary_by_day(self, user, days):
        current_day = now().replace(hour=0, minute=0, second=0, microsecond=0)
        document_summary_by_day = DocumentCompare.objects \
            .filter(document__created_by=user, created_at__gte=current_day + timedelta(days=-days)) \
            .annotate(day=TruncDay('created_at')).values('day') \
            .annotate(file_count=Count('document__created_by'))
        data = self.split_data_by_day(document_summary_by_day)
        return data

    def get_legal_search_summary_by_day(self, user, days):
        current_day = now().replace(hour=0, minute=0, second=0, microsecond=0)
        legal_search_summary_by_day = LegalSearchHistory.objects.filter(deleted_time=None, requested_by=user,
                                                                        created_time__gte=current_day + timedelta(
                                                                            days=-days)) \
            .annotate(day=TruncDay('created_time')).values('day') \
            .annotate(response_time_avg=Coalesce(Avg('response_time'), 0.0), num_requests=Count('requested_by'))
        data = self.split_data_by_day(legal_search_summary_by_day)
        return data

    def get_summary(self, ocr_summary_by_day, document_summary_by_day, legal_search_summary_by_day,
                    day_str):
        try:
            ocr_summary = ocr_summary_by_day[day_str]
        except KeyError:
            ocr_summary = {
                "docx_size_mb": 0,
                "file_count": 0
            }
        try:
            document_summary = document_summary_by_day[day_str]
        except KeyError:
            document_summary = {
                "file_count": 0
            }
        try:
            legal_search_summary = legal_search_summary_by_day[day_str]
        except KeyError:
            legal_search_summary = {
                "response_time_avg": 0.0,
                "num_requests": 0,
            }

        data = {
            "ocr": ocr_summary,
            "document": document_summary,
            "legal_search": legal_search_summary,
        }
        return data
    
    def get(self, request, *args, **kwargs):
        days = request.GET.get('days', 0)
        try:
            days = int(days)
        except ValueError:
            return Response({'message': 'days must be a number'}, status=status.HTTP_400_BAD_REQUEST)

        user = request.user
        ocr_summary_by_day = self.get_ocr_summary_by_day(user, days)
        document_summary_by_day = self.get_document_summary_by_day(user, days)
        legal_search_summary_by_day = self.get_legal_search_summary_by_day(user, days)

        current_day = now().replace(hour=0, minute=0, second=0, microsecond=0)

        # Dùng list comprehension để tạo danh sách ngày
        date_list = [(current_day - timedelta(days=i)).strftime("%d/%m/%Y") for i in range(days, -1, -1)]

        data = [
            {'date': day_str, 'data': self.get_summary(ocr_summary_by_day, document_summary_by_day, legal_search_summary_by_day, day_str)}
            for day_str in date_list
        ]

        return Response(data, status=status.HTTP_200_OK)

class ActivityAdminUserSummaryView(APIView):
    permission_classes = [IsAdminUser]

    def get(self, request, *args, **kwargs):
        query_set = User.objects.all()
        total_user = query_set.count()
        current_day = now().replace(hour=0, minute=0, second=0, microsecond=0)
        new_user_day = query_set.filter(created_time__gte=current_day + relativedelta(days=-1)).count()
        new_user_month = query_set.filter(created_time__gte=current_day + relativedelta(months=-1)).count()
        new_user_year = query_set.filter(created_time__gte=current_day + relativedelta(years=-1)).count()
        data = {
            "total_user": total_user,
            "new_user_day": new_user_day,
            "new_user_month": new_user_month,
            "new_user_year": new_user_year,
        }
        return Response(data, status=status.HTTP_200_OK)


class ActivityAdminSummaryView(APIView):
    permission_classes = [IsAdminUser]

    def get_ocr_summary(self, ocr_summary):
        if len(ocr_summary) != 0:
            ocr_summary = ocr_summary[0]
            ocr_summary.pop('created_by')
        else:
            ocr_summary = {
                "docx_size_mb": 0,
                "file_count": 0
            }

        return ocr_summary

    def get_document_summary(self, document_summary):
        if len(document_summary) != 0:
            document_summary = document_summary[0]
            document_summary.pop('document__created_by')
        else:
            document_summary = {
                "file_count": 0
            }
        return document_summary

    def get_legal_search_summary(self, legal_search_summary):
        if len(legal_search_summary) != 0:
            legal_search_summary = legal_search_summary[0]
            legal_search_summary.pop('requested_by')
        else:
            legal_search_summary = {
                "response_time_avg": 0.0,
                "num_requests": 0,
            }
        return legal_search_summary

    def get_conflict_summary(self, conflict_summary):
        if len(conflict_summary) != 0:
            conflict_summary = conflict_summary[0]
            conflict_summary.pop('document__created_by')
        else:
            conflict_summary = {
                "num_requests": 0
            }

        return conflict_summary

    def get(self, request, *args, **kwargs):
        user_id = request.GET.get('user_id', 0)
        
        if not user_id:
            return Response({"error": "user_id is required"}, status=status.HTTP_400_BAD_REQUEST)
        
        try:
            user = User.objects.exclude(pk=request.user.id).get(id=user_id)
        except User.DoesNotExist:
            return Response({"error": "User not found"}, status=status.HTTP_404_NOT_FOUND)

        # Query only for specific user
        ocr_summary = Document.objects.filter(
            deleted_at__isnull=True,
            created_by=user_id
        ).values('created_by').annotate(
            docx_size_mb=Value(0),
            file_count=Count('created_by')
        ).first() or {}

        document_summary = DocumentCompare.objects.filter(
            document__created_by=user_id
        ).values('document__created_by').annotate(
            file_count=Count('document__created_by')
        ).first() or {}

        legal_search_summary = LegalSearchHistory.objects.filter(
            deleted_time__isnull=True,
            requested_by=user_id
        ).values('requested_by').annotate(
            response_time_avg=Coalesce(Avg('response_time'), 0.0),
            num_requests=Count('requested_by')
        ).first() or {}

        conflict_summary = DocumentAuthority.objects.filter(
            deleted_at__isnull=True,
            document__created_by=user_id
        ).values('document__created_by').annotate(
            num_requests=Count('document__created_by')
        ).first() or {}

        # Create single user summary
        user_summary = {
            "user": user.email,
            "user_id": user.id,
            "fullname": user.fullname,
            "data": {
                "ocr": self.get_ocr_summary([ocr_summary]),
                "document": self.get_document_summary([document_summary]),
                "legal_search": self.get_legal_search_summary([legal_search_summary]),
                "conflict": self.get_conflict_summary([conflict_summary])
            }
        }

        return Response(user_summary, status=status.HTTP_200_OK)


class ActivityAdminByDayView(APIView):
    permission_classes = [IsAdminUser]

    def get_summary(self, ocr_summary_by_day, document_summary_by_day, legal_search_summary_by_day,
                    day, user):
        try:
            ocr_summary = ocr_summary_by_day.get(day__gte=day, day__lte=day + relativedelta(days=1), created_by=user)
        except:
            ocr_summary = {
                "docx_size_mb": 0,
                "file_count": 0
            }
        try:
            document_summary = document_summary_by_day.get(document__created_by=user, day__gte=day, day__lte=day + relativedelta(days=1))
        except:
            document_summary = {
                "file_count": 0
            }
        try:
            legal_search_summary = legal_search_summary_by_day.get(requested_by=user, day__gte=day, day__lte=day + relativedelta(days=1))
        except:
            legal_search_summary = {
                "response_time_avg": 0.0,
                "num_requests": 0,
            }
        data = {
            "ocr": ocr_summary,
            "document": document_summary,
            "legal_search": legal_search_summary,
        }
        return data

    def get(self, request, *args, **kwargs):
        days = request.GET.get('days', 0)
        cache_key = f'summary_data_{days}'
        
        cached_data = cache.get(cache_key)
        if cached_data:
            return Response(cached_data, status=status.HTTP_200_OK)
            
        # Validate days parameter
        try:
            days = int(request.GET.get('days', 0))
        except ValueError:
            return Response({'message': 'days must be a number'}, status=status.HTTP_400_BAD_REQUEST)

        if days < 0 or days > 15:  # Add reasonable limit
            return Response({'message': 'days must be between 0 and 30'}, status=status.HTTP_400_BAD_REQUEST)

        current_day = now().replace(hour=0, minute=0, second=0, microsecond=0)
        start_date = current_day - timedelta(days=days)

        # Single query for user emails
        users = User.objects.exclude(pk=request.user.id).values('id', 'email')
        user_dict = {user['id']: user['email'] for user in users}

        # Optimize queries with specific date range and needed fields only
        ocr_data = (Document.objects
                    .filter(
                        is_convert=True,
                        deleted_at__isnull=True,
                        created_at__gte=start_date,
                        created_at__lte=current_day
                    )
                    .annotate(day=TruncDay('created_at'))
                    .values('day', 'created_by')
                    .annotate(count=Count('id'))
                    .order_by('day'))

        doc_data = (DocumentCompare.objects
                    .filter(
                        created_at__gte=start_date,
                        created_at__lte=current_day
                    )
                    .annotate(day=TruncDay('created_at'))
                    .values('day', 'document__created_by')
                    .annotate(count=Count('id'))
                    .order_by('day'))

        legal_data = (LegalSearchHistory.objects
                    .filter(
                        deleted_time__isnull=True,
                        created_time__gte=start_date,
                        created_time__lte=current_day
                    )
                    .annotate(day=TruncDay('created_time'))
                    .values('day', 'requested_by')
                    .annotate(
                        count=Count('id'),
                        avg_response=Coalesce(Avg('response_time'), 0.0)
                    )
                    .order_by('day'))

        # Create date range for all days
        date_range = [(current_day - timedelta(days=i)).strftime("%d/%m/%Y") 
                    for i in range(days, -1, -1)]

        # Pre-process data into dictionaries for O(1) lookup
        ocr_dict = defaultdict(lambda: defaultdict(int))
        doc_dict = defaultdict(lambda: defaultdict(int))
        legal_dict = defaultdict(lambda: {'count': 0, 'avg_response': 0})

        for item in ocr_data:
            day_str = item['day'].strftime("%d/%m/%Y")
            ocr_dict[item['created_by']][day_str] = item['count']

        for item in doc_data:
            day_str = item['day'].strftime("%d/%m/%Y")
            doc_dict[item['document__created_by']][day_str] = item['count']

        for item in legal_data:
            day_str = item['day'].strftime("%d/%m/%Y")
            legal_dict[f"{item['requested_by']}_{day_str}"] = {
                'count': item['count'],
                'avg_response': item['avg_response']
            }

        # Build final response
        all_data = []
        for user_id, email in user_dict.items():
            user_data = []
            for date_str in date_range:
                user_data.append({
                    'date': date_str,
                    'data': {
                        'ocr': {
                            'file_count': ocr_dict[user_id][date_str],
                            'docx_size_mb': 0
                        },
                        'document': {
                            'file_count': doc_dict[user_id][date_str]
                        },
                        'legal_search': {
                            'num_requests': legal_dict[f"{user_id}_{date_str}"]['count'],
                            'response_time_avg': legal_dict[f"{user_id}_{date_str}"]['avg_response']
                        }
                    }
                })

            all_data.append({
                'user': email,
                'data': user_data
            })
            
        cache.set(cache_key, all_data, timeout=300)  # Cache for 5 minutes
        return Response(all_data, status=status.HTTP_200_OK)