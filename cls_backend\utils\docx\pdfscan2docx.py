import os
import shutil
import zipfile

import requests

from cls_backend.settings import OCR_HOST, PRIVATE_MEDIA_ROOT


def check_status_process_file(session_id):
    print("Đang check {}".format(session_id))
    url = f"{OCR_HOST}/check_process_status"
    payload = {'session_id': session_id,
               'return_type': 'file'}
    response = requests.request("POST", url, data=payload)
    data = response.json()
    print(data)
    return data["percentage"]


def get_processed_file_docx(session_upload_folder):
    payload = {'session_upload_folder': session_upload_folder}
    response = requests.post(f"{OCR_HOST}/get_session_upload_folder", data=payload)
    if response.status_code == 200:
        content_type = response.headers['content-type']
        if 'application/octet-stream' in content_type:
            download_path = os.path.join(PRIVATE_MEDIA_ROOT, session_upload_folder)
            if not os.path.exists(download_path):
                os.makedirs(download_path)
            filename = download_path + "/downloaded_file.zip"
            print("Đang ghi vào folder {}!!!!!".format(filename))
            with open(filename, 'wb') as file:
                file.write(response.content)
            return filename
        else:
            print("Response is not a binary file.")
    else:
        print("Request failed with status code {response.status_code}")
    return None


def unzip_and_get_docx_path(zip_file_path):
    # Get the directory part of the zip file path (same path as the zip file)
    extract_dir = os.path.dirname(zip_file_path)

    with zipfile.ZipFile(zip_file_path, 'r') as zip_ref:
        # Look for the first .docx file and extract it
        docx_file = next((f for f in zip_ref.namelist() if f.endswith('.docx')), None)

        if docx_file:
            # Extract and return the full path of the .docx file
            zip_ref.extract(docx_file, extract_dir)
            return os.path.join(extract_dir, docx_file)

    return None


def convert_scan2_docx(pdf_path):
    file_name = os.path.basename(pdf_path)
    files = [
        ('file', (file_name, open(pdf_path, 'rb'), 'application/pdf'))
    ]

    payload = {
        'return_type': 'word'
    }

    response = requests.post(f"{OCR_HOST}/ocr/extract_ocr", files=files, data=payload, verify=False)
    if response.status_code == 200:
        base = os.path.splitext(pdf_path)[0]
        docx_path = f"{base}.docx"
        with open(docx_path, 'wb') as file:
            file.write(response.content)
        return docx_path

    print(f"API request failed with status code {response.status_code}")
    return None


def remove_folder_containing_docx(docx_file_path):
    # Get the directory of the docx file
    directory = os.path.dirname(docx_file_path)

    # Check if the directory exists
    if os.path.exists(directory):
        # Remove the directory and all its contents
        shutil.rmtree(directory)
        print(f"Removed directory: {directory}")
    else:
        print(f"Directory does not exist: {directory}")

