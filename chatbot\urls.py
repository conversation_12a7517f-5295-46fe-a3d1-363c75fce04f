from django.urls import include, re_path, path
from rest_framework import routers
from chatbot.views import ConversationViewSet

# Routers provide a way of automatically determining the URL conf.
router = routers.DefaultRouter()
# router.register("chat", LLMAPIView)
# Wire up our API using automatic URL routing.
# Additionally, we include login URLs for the browsable API.
router.register('conversation', ConversationViewSet, basename='conversation') 
urlpatterns = [
    path('', include(router.urls)),
]