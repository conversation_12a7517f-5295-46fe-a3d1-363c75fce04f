from .models import MaintenancePlan
import time
from django.utils import timezone
from .constants import *
from .serializers import MaintenancePlanSerializer
from rest_framework.response import Response
from rest_framework import status
from rest_framework.renderers import J<PERSON><PERSON>enderer


class MaintenanceMiddleware:
    def __init__(self, get_response):
        self.get_response = get_response
        # One-time configuration and initialization.

    def __call__(self, request):
        print(request.path)
        if request.path.startswith('/admin'):
            return self.get_response(request)
        now = timezone.now()
        query = MaintenancePlan.objects.filter(
            deleted_at__isnull=True,
        ).filter(
            status__in=[STATUS_SCHEDULED, STATUS_IN_PROGRESS],
            planned_start_time__lte=now,
            planned_end_time__gte=now
        ).order_by('planned_start_time')

        if query.exists():
            active_plan = query.first()
            serializer = MaintenancePlanSerializer(active_plan)
            response = {
                'status': 'maintenance',
                'message': 'Maintenance is in progress',
                'data': serializer.data
            }
            response = Response(response, status=status.HTTP_503_SERVICE_UNAVAILABLE)
            response.accepted_renderer = JSONRenderer()
            response.accepted_media_type = "application/json"
            response.renderer_context = {}
            response.render()
            return response
        else:
            response = self.get_response(request)
            return response
