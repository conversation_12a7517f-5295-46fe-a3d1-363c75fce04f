from rest_framework import serializers

from cls_backend.models import Document, DocumentAuthority
from cls_backend.utils.file_validator import FileValidator
from cls_backend.utils.file_utils import generate_presigned_url


UPLOAD_MAX_SIZE = 200 * 1024 * 1024  # 200 MB in bytes
validate_file = FileValidator(max_size=UPLOAD_MAX_SIZE,
                              content_types=[
                                #   'application/pdf',  # pdf
                                #   'application/vnd.openxmlformats-officedocument.wordprocessingml.document',  # docx
                                #   'application/msword'  # docx
                              ])


class DocumentAuthoritySerializer(serializers.ModelSerializer):
    origin = serializers.FileField(validators=[validate_file], required=True)
    status = serializers.CharField(source='authority_status', read_only=True)
    authority_status_display = serializers.CharField(source='get_authority_status_display', read_only=True)
    created_at = serializers.DateTimeField(format="%Y-%m-%d %H:%M:%S", read_only=True)
    name = serializers.Char<PERSON>ield(read_only=True)

    class Meta:
        model = Document
        fields = [
            'id', 'origin', 'status', 'authority_status_display', 'created_at', 'name',
            'noi_dung_done', 'hinh_thuc_done', 'hieu_luc_done', 'khac_biet_done'
        ]
        read_only_fields = [
            'id', 'origin', 'status', 'created_at', 'name',
            'noi_dung_done', 'hinh_thuc_done', 'hieu_luc_done', 'khac_biet_done'
        ]


class DocumentSerializer(serializers.ModelSerializer):
    user_id = serializers.IntegerField(source='document.created_by.id')

    class Meta:
        model = Document
        fields = ['id', 'json_data', 'user_id']


class DocumentAuthorityLessInfoSerializer(serializers.ModelSerializer):
    id = serializers.IntegerField(source='document.id')
    name = serializers.CharField(source='document.name')
    # origin = serializers.FileField(source='document.origin')
    origin = serializers.SerializerMethodField(read_only=True)
    updated_at = serializers.DateTimeField(format="%Y-%m-%d %H:%M:%S", read_only=True)
    # status = serializers.CharField(source="document.get_authority_status_display", read_only=True)
    status = serializers.CharField(source="document.authority_status", read_only=True)
    authority_status_display = serializers.CharField(source="document.get_authority_status_display", read_only=True)

    def get_origin(self, obj):
        return generate_presigned_url(obj.document.origin)

    class Meta:
        model = DocumentAuthority
        fields = ["id", "name", "updated_at", 'origin', 'status', 'authority_status_display']
        read_only_fields = ["id", "name", "updated_at", 'origin', 'status']


class RetrieveDocumentAuthoritySerializer(serializers.ModelSerializer):
    name = serializers.CharField(source='document.name', read_only=True)
    updated_at = serializers.DateTimeField(format="%Y-%m-%d %H:%M:%S", read_only=True)
    # origin = serializers.FileField(read_only=True)
    origin = serializers.SerializerMethodField(read_only=True)

    def get_origin(self, obj):
        return generate_presigned_url(obj.document.origin)

    class Meta:
        model = DocumentAuthority
        fields = ['id', 'origin', 'updated_at', 'name', 'hinh_thuc', 'noi_dung', 'hieu_luc', 'khac_biet']
        read_only_fields = ['id', 'origin', 'updated_at', 'name', 'hinh_thuc', 'noi_dung', 'hieu_luc', 'khac_biet']
