# Generated by Django 4.2 on 2025-05-08 07:55

from django.conf import settings
import django.contrib.postgres.fields
from django.db import migrations, models
import django.db.models.deletion
import uuid


class Migration(migrations.Migration):

    initial = True

    dependencies = [
        ("auth", "0012_alter_user_first_name_max_length"),
    ]

    operations = [
        migrations.CreateModel(
            name="User",
            fields=[
                (
                    "id",
                    models.BigAutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                (
                    "last_login",
                    models.DateTimeField(
                        blank=True, null=True, verbose_name="last login"
                    ),
                ),
                (
                    "is_superuser",
                    models.BooleanField(
                        default=False,
                        help_text="Designates that this user has all permissions without explicitly assigning them.",
                        verbose_name="superuser status",
                    ),
                ),
                ("email", models.CharField(max_length=255, unique=True)),
                ("password", models.CharField(max_length=255)),
                (
                    "avatar",
                    models.FileField(blank=True, null=True, upload_to="avatars"),
                ),
                ("fullname", models.CharField(max_length=255)),
                ("created_time", models.DateTimeField(auto_now_add=True)),
                ("updated_time", models.DateTimeField(auto_now=True)),
                ("deleted_time", models.DateTimeField(blank=True, null=True)),
                ("verification_code", models.TextField(blank=True, null=True)),
                (
                    "verification_code_created_at",
                    models.DateTimeField(blank=True, null=True),
                ),
                (
                    "is_staff",
                    models.BooleanField(
                        default=False,
                        help_text="Designates whether the user can log into this admin site.",
                    ),
                ),
                (
                    "is_active",
                    models.BooleanField(
                        default=True,
                        help_text="Designates whether this user should be treated as active. Unselect this instead of deleting accounts.",
                    ),
                ),
                (
                    "groups",
                    models.ManyToManyField(
                        blank=True,
                        help_text="The groups this user belongs to. A user will get all permissions granted to each of their groups.",
                        related_name="user_set",
                        related_query_name="user",
                        to="auth.group",
                        verbose_name="groups",
                    ),
                ),
                (
                    "user_permissions",
                    models.ManyToManyField(
                        blank=True,
                        help_text="Specific permissions for this user.",
                        related_name="user_set",
                        related_query_name="user",
                        to="auth.permission",
                        verbose_name="user permissions",
                    ),
                ),
            ],
            options={
                "abstract": False,
            },
        ),
        migrations.CreateModel(
            name="Document",
            fields=[
                ("created_at", models.DateTimeField(auto_now_add=True)),
                ("updated_at", models.DateTimeField(auto_now=True)),
                ("deleted_at", models.DateTimeField(blank=True, null=True)),
                ("ID", models.IntegerField(blank=True, null=True)),
                ("task_id", models.UUIDField(default=uuid.uuid4)),
                ("es_id", models.IntegerField(blank=True, null=True)),
                ("title", models.CharField(blank=True, max_length=500, null=True)),
                ("dia_danh", models.CharField(blank=True, max_length=200, null=True)),
                ("ngay_ban_hanh", models.DateTimeField(blank=True, null=True)),
                ("ngay_co_hieu_luc", models.DateTimeField(blank=True, null=True)),
                ("ngay_dang_cong_bao", models.DateTimeField(blank=True, null=True)),
                ("ngay_het_hieu_luc", models.DateTimeField(blank=True, null=True)),
                (
                    "ngay_het_hieu_luc_mot_phan",
                    models.DateTimeField(blank=True, null=True),
                ),
                ("so_hieu", models.CharField(blank=True, max_length=200, null=True)),
                ("toan_van", models.TextField(blank=True, null=True)),
                ("trich_yeu", models.CharField(blank=True, max_length=1000, null=True)),
                (
                    "tinh_trang_hieu_luc",
                    models.CharField(
                        blank=True,
                        choices=[
                            ("Còn hiệu lực", "Còn hiệu lực"),
                            ("Hết hiệu lực một phần", "Hết hiệu lực một phần"),
                            ("Chưa có hiệu lực", "Chưa có hiệu lực"),
                            ("Hết hiệu lực toàn bộ", "Hết hiệu lực toàn bộ"),
                            ("Ngưng hiệu lực", "Ngưng hiệu lực"),
                            ("Chưa xác định", "Chưa xác định"),
                            ("Ngưng hiệu lực một phần", "Ngưng hiệu lực một phần"),
                            ("Không còn phù hợp", "Không còn phù hợp"),
                        ],
                        max_length=100,
                        null=True,
                    ),
                ),
                (
                    "co_quan_ban_hanh",
                    models.CharField(blank=True, max_length=100, null=True),
                ),
                ("nguoi_ky", models.CharField(blank=True, max_length=100, null=True)),
                (
                    "loai_van_ban",
                    models.CharField(
                        blank=True,
                        choices=[
                            ("Quyết định", "Quyết định"),
                            ("Thông tư", "Thông tư"),
                            ("Nghị quyết", "Nghị quyết"),
                            ("Nghị định", "Nghị định"),
                            ("Thông tư liên tịch", "Thông tư liên tịch"),
                            ("Luật", "Luật"),
                            ("Văn bản hợp nhất", "Văn bản hợp nhất"),
                            ("Pháp lệnh", "Pháp lệnh"),
                            ("Công văn", "Công văn"),
                            ("Bộ luật", "Bộ luật"),
                            ("Nghị quyết liên tịch", "Nghị quyết liên tịch"),
                            ("Chỉ thị", "Chỉ thị"),
                            ("Văn bản khác", "Văn bản khác"),
                            ("Lệnh", "Lệnh"),
                            ("Hiến pháp", "Hiến pháp"),
                            ("Văn bản liên quan", "Văn bản liên quan"),
                            ("Thông báo", "Thông báo"),
                            ("Chương trình", "Chương trình"),
                            ("Sắc lệnh", "Sắc lệnh"),
                            ("Thông tư liên bộ", "Thông tư liên bộ"),
                            ("Hiệp định", "Hiệp định"),
                            ("Sắc luật", "Sắc luật"),
                        ],
                        max_length=50,
                        null=True,
                    ),
                ),
                (
                    "id",
                    models.UUIDField(
                        default=uuid.uuid4,
                        editable=False,
                        primary_key=True,
                        serialize=False,
                    ),
                ),
                (
                    "types",
                    models.IntegerField(
                        choices=[(0, "BY_USER"), (1, "BY_AI")], default=0
                    ),
                ),
                ("name", models.CharField(blank=True, max_length=1000, null=True)),
                (
                    "origin",
                    models.FileField(
                        blank=True, null=True, upload_to="document/%Y/%m/%d"
                    ),
                ),
                (
                    "convert",
                    models.FileField(
                        blank=True, null=True, upload_to="document/%Y/%m/%d"
                    ),
                ),
                ("is_convert", models.BooleanField(default=False)),
                ("convert_percentage", models.IntegerField(default=0)),
                (
                    "convert_datetime",
                    models.CharField(blank=True, max_length=50, null=True),
                ),
                ("convert_session_id", models.UUIDField(blank=True, null=True)),
                (
                    "status",
                    models.SmallIntegerField(
                        choices=[
                            (0, "Thất bại"),
                            (1, "Thành công"),
                            (2, "Đang xử lý"),
                            (3, "Chờ xử lý"),
                        ],
                        default=3,
                    ),
                ),
                ("json_data", models.JSONField(blank=True, null=True)),
                ("convert_json", models.BooleanField(default=False)),
                (
                    "compare_status",
                    models.SmallIntegerField(
                        choices=[
                            (0, "Thất bại"),
                            (1, "Thành công"),
                            (2, "Đang xử lý"),
                            (3, "Chờ xử lý"),
                            (4, "Chưa đối chiếu"),
                        ],
                        default=4,
                    ),
                ),
                (
                    "authority_status",
                    models.SmallIntegerField(
                        choices=[
                            (0, "Thất bại"),
                            (1, "Thành công"),
                            (2, "Đang xử lý"),
                            (3, "Chờ xử lý"),
                            (4, "Chưa kiểm tra"),
                            (5, "Không tìm thấy"),
                        ],
                        default=4,
                    ),
                ),
                ("noi_dung_done", models.BooleanField(default=False)),
                ("hinh_thuc_done", models.BooleanField(default=False)),
                ("hieu_luc_done", models.BooleanField(default=False)),
                ("khac_biet_done", models.BooleanField(default=False)),
                ("ie_done", models.BooleanField(default=False)),
                (
                    "created_by",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.CASCADE,
                        to=settings.AUTH_USER_MODEL,
                    ),
                ),
                (
                    "related_documents",
                    models.ManyToManyField(
                        blank=True,
                        null=True,
                        related_name="related_documents",
                        to="cls_backend.document",
                    ),
                ),
            ],
            options={
                "abstract": False,
            },
        ),
        migrations.CreateModel(
            name="Package",
            fields=[
                (
                    "id",
                    models.CharField(max_length=20, primary_key=True, serialize=False),
                ),
                (
                    "upload_type",
                    models.CharField(
                        blank=True, default="docx,pdf", max_length=50, null=True
                    ),
                ),
                (
                    "download_type",
                    models.CharField(
                        blank=True, default="docx,pdf", max_length=50, null=True
                    ),
                ),
                (
                    "max_file_size_per_upload",
                    models.IntegerField(blank=True, default=5000, null=True),
                ),
                (
                    "max_num_doc_per_upload",
                    models.IntegerField(blank=True, default=5, null=True),
                ),
                (
                    "description",
                    models.CharField(blank=True, max_length=1000, null=True),
                ),
            ],
        ),
        migrations.CreateModel(
            name="WorkSpace",
            fields=[
                ("created_at", models.DateTimeField(auto_now_add=True)),
                ("updated_at", models.DateTimeField(auto_now=True)),
                ("deleted_at", models.DateTimeField(blank=True, null=True)),
                (
                    "id",
                    models.UUIDField(
                        default=uuid.uuid4,
                        editable=False,
                        primary_key=True,
                        serialize=False,
                    ),
                ),
                ("name", models.CharField(max_length=100)),
                ("description", models.CharField(max_length=500)),
                (
                    "user",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.CASCADE,
                        to=settings.AUTH_USER_MODEL,
                    ),
                ),
            ],
            options={
                "abstract": False,
            },
        ),
        migrations.CreateModel(
            name="Role",
            fields=[
                (
                    "id",
                    models.BigAutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                ("created_at", models.DateTimeField(auto_now_add=True)),
                ("updated_at", models.DateTimeField(auto_now=True)),
                ("deleted_at", models.DateTimeField(blank=True, null=True)),
                ("name", models.CharField(max_length=100)),
                ("description", models.CharField(max_length=500)),
                (
                    "workspaces",
                    models.ManyToManyField(
                        blank=True,
                        null=True,
                        related_name="role_workspaces",
                        to="cls_backend.workspace",
                    ),
                ),
            ],
            options={
                "abstract": False,
            },
        ),
        migrations.CreateModel(
            name="Note",
            fields=[
                ("created_at", models.DateTimeField(auto_now_add=True)),
                ("updated_at", models.DateTimeField(auto_now=True)),
                ("deleted_at", models.DateTimeField(blank=True, null=True)),
                (
                    "id",
                    models.UUIDField(
                        default=uuid.uuid4,
                        editable=False,
                        primary_key=True,
                        serialize=False,
                    ),
                ),
                (
                    "document_name",
                    models.CharField(blank=True, max_length=250, null=True),
                ),
                (
                    "clause_detail",
                    models.CharField(blank=True, max_length=250, null=True),
                ),
                ("content", models.TextField(blank=True, null=True)),
                (
                    "workspace",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.CASCADE,
                        related_name="notes",
                        to="cls_backend.workspace",
                    ),
                ),
            ],
            options={
                "abstract": False,
            },
        ),
        migrations.CreateModel(
            name="LegalSearchHistory",
            fields=[
                ("id", models.BigAutoField(primary_key=True, serialize=False)),
                ("query", models.TextField()),
                ("result_count", models.IntegerField(default=0)),
                ("response_time", models.FloatField()),
                (
                    "domain_list",
                    django.contrib.postgres.fields.ArrayField(
                        base_field=models.IntegerField(), size=None
                    ),
                ),
                ("created_time", models.DateTimeField(auto_now_add=True)),
                ("deleted_time", models.DateTimeField(null=True)),
                ("rate", models.IntegerField(default=-1)),
                ("comment", models.TextField(null=True)),
                (
                    "requested_by",
                    models.ForeignKey(
                        default=None,
                        null=True,
                        on_delete=django.db.models.deletion.CASCADE,
                        to=settings.AUTH_USER_MODEL,
                    ),
                ),
            ],
        ),
        migrations.CreateModel(
            name="LawClause",
            fields=[
                (
                    "id",
                    models.BigAutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                ("created_at", models.DateTimeField(auto_now_add=True)),
                ("updated_at", models.DateTimeField(auto_now=True)),
                ("deleted_at", models.DateTimeField(blank=True, null=True)),
                (
                    "status",
                    models.SmallIntegerField(
                        blank=True,
                        choices=[
                            (4, "Chưa rà soát"),
                            (1, "Thành công"),
                            (3, "Chờ xử lý"),
                            (2, "Đang xử lý"),
                            (0, "Thất bại"),
                        ],
                        null=True,
                    ),
                ),
                (
                    "type",
                    models.CharField(
                        choices=[
                            ("tham_quyen_noi_dung", "Thẩm quyền nội dung"),
                            ("dieu_khoan_lien_quan", "Điều khoản liên quan"),
                        ],
                        default=4,
                    ),
                ),
                ("clause_id", models.CharField(blank=True, max_length=100, null=True)),
                ("law_title", models.CharField(blank=True, max_length=500, null=True)),
                ("title", models.CharField(blank=True, max_length=2000, null=True)),
                ("reason", models.CharField(blank=True, max_length=200, null=True)),
                ("position", models.CharField(blank=True, max_length=100, null=True)),
                ("content", models.TextField(blank=True, null=True)),
                ("show_content", models.TextField(blank=True, null=True)),
                ("doc_id", models.CharField(blank=True, max_length=100, null=True)),
                ("is_raw", models.BooleanField(blank=True, null=True)),
                ("result", models.CharField(blank=True, max_length=100, null=True)),
                ("ai_result", models.JSONField(blank=True, null=True)),
                ("different_status", models.BooleanField(blank=True, null=True)),
                ("ly_do", models.TextField(blank=True, null=True)),
                ("ket_luan", models.TextField(blank=True, null=True)),
                ("giai_phap", models.TextField(blank=True, null=True)),
                (
                    "document",
                    models.ForeignKey(
                        blank=True,
                        null=True,
                        on_delete=django.db.models.deletion.CASCADE,
                        to="cls_backend.document",
                    ),
                ),
                (
                    "related_clauses",
                    models.ManyToManyField(
                        blank=True,
                        null=True,
                        related_name="related_clauses",
                        to="cls_backend.lawclause",
                    ),
                ),
            ],
            options={
                "abstract": False,
            },
        ),
        migrations.CreateModel(
            name="HistorySearchResult",
            fields=[
                (
                    "id",
                    models.BigAutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                ("data", models.TextField()),
                ("params", models.TextField()),
                ("search_time", models.DateTimeField(auto_now_add=True)),
                (
                    "type",
                    models.CharField(blank=True, default="AI_SEARCH", max_length=50),
                ),
                (
                    "user",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.CASCADE,
                        to=settings.AUTH_USER_MODEL,
                    ),
                ),
            ],
            options={
                "ordering": ["-search_time"],
            },
        ),
        migrations.CreateModel(
            name="DocumentRelated",
            fields=[
                (
                    "id",
                    models.UUIDField(
                        default=uuid.uuid4,
                        editable=False,
                        primary_key=True,
                        serialize=False,
                    ),
                ),
                (
                    "relation_type",
                    models.CharField(
                        choices=[
                            ("van_ban_bi_bai_bo", "Văn bản bị bãi bỏ"),
                            (
                                "van_ban_bi_bai_bo_mot_phan",
                                "Văn bản bị bãi bỏ một phần",
                            ),
                            ("van_ban_bi_dinh_chi", "Văn bản bị đình chỉ"),
                            (
                                "van_ban_bi_dinh_chi_mot_phan",
                                "Văn bản bị đình chỉ một phần",
                            ),
                            ("van_ban_bi_dinh_chinh", "Văn bản bị đính chính"),
                            ("van_ban_bi_huy_bo", "Văn bản bị huỷ bỏ"),
                            (
                                "van_ban_bi_huy_bo_mot_phan",
                                "Văn bản bị huỷ bỏ một phần",
                            ),
                            ("van_ban_bi_thay_the", "Văn bản bị thay thế"),
                            (
                                "van_ban_bi_thay_the_mot_phan",
                                "Văn bản bị thay thế một phần",
                            ),
                            ("van_ban_can_cu", "Văn bản căn cứ"),
                            ("van_ban_chua_xac_dinh", "Văn bản chưa xác định"),
                            (
                                "van_ban_chua_xac_dinh_mot_phan",
                                "Văn bản chưa xác định một phần",
                            ),
                            ("van_ban_dan_chieu", "Văn bản dẫn chiếu"),
                            ("van_ban_duoc_huong_dan", "Văn bản được hướng dẫn"),
                            ("van_ban_duoc_sua_doi", "Văn bản được sửa đổi"),
                            (
                                "van_ban_duoc_sua_doi_bo_sung",
                                "Văn bản được sửa đổi, bổ sung",
                            ),
                            ("van_ban_lien_quan_khac", "Văn bản liên quan khác"),
                            ("van_ban_quy_dinh_chi_tiet", "Văn bản quy định chi tiết"),
                        ],
                        max_length=200,
                    ),
                ),
                ("ID", models.IntegerField(blank=True, null=True)),
                ("score", models.FloatField(blank=True, null=True)),
                ("url", models.URLField(blank=True, null=True)),
                ("title", models.CharField(blank=True, max_length=1000, null=True)),
                ("toan_van", models.TextField(blank=True, null=True)),
                ("pham_vi", models.CharField(blank=True, max_length=1000, null=True)),
                ("vbpl_id", models.CharField(blank=True, max_length=100, null=True)),
                ("nguoi_ky", models.CharField(blank=True, max_length=255, null=True)),
                (
                    "don_vi",
                    django.contrib.postgres.fields.ArrayField(
                        base_field=models.CharField(max_length=255),
                        blank=True,
                        null=True,
                        size=None,
                    ),
                ),
                ("so_hieu", models.CharField(blank=True, max_length=255, null=True)),
                ("ten_van_ban", models.CharField(max_length=1000)),
                ("trich_yeu", models.TextField(blank=True, null=True)),
                ("loai_van_ban", models.CharField(max_length=100)),
                ("ngay_ap_dung", models.DateTimeField(blank=True, null=True)),
                ("ngay_ban_hanh", models.DateTimeField(blank=True, null=True)),
                (
                    "co_quan_ban_hanh",
                    models.CharField(blank=True, max_length=255, null=True),
                ),
                ("ngay_co_hieu_luc", models.DateTimeField(blank=True, null=True)),
                (
                    "tinh_trang_hieu_luc",
                    models.CharField(blank=True, max_length=100, null=True),
                ),
                ("noi_dung", models.TextField(blank=True, null=True)),
                (
                    "document",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.CASCADE,
                        to="cls_backend.document",
                    ),
                ),
            ],
        ),
        migrations.CreateModel(
            name="DocumentCompare",
            fields=[
                ("created_at", models.DateTimeField(auto_now_add=True)),
                ("updated_at", models.DateTimeField(auto_now=True)),
                ("deleted_at", models.DateTimeField(blank=True, null=True)),
                ("id", models.BigAutoField(primary_key=True, serialize=False)),
                ("document_based", models.JSONField(blank=True, null=True)),
                ("document_related", models.JSONField(blank=True, null=True)),
                ("dieu_khoan", models.JSONField(blank=True, null=True)),
                (
                    "document",
                    models.ForeignKey(
                        blank=True,
                        null=True,
                        on_delete=django.db.models.deletion.CASCADE,
                        to="cls_backend.document",
                    ),
                ),
            ],
            options={
                "abstract": False,
            },
        ),
        migrations.CreateModel(
            name="DocumentAuthority",
            fields=[
                ("created_at", models.DateTimeField(auto_now_add=True)),
                ("updated_at", models.DateTimeField(auto_now=True)),
                ("deleted_at", models.DateTimeField(blank=True, null=True)),
                ("id", models.BigAutoField(primary_key=True, serialize=False)),
                ("noi_dung", models.JSONField(blank=True, null=True)),
                ("hinh_thuc", models.JSONField(blank=True, null=True)),
                ("hieu_luc", models.JSONField(blank=True, null=True)),
                ("khac_biet", models.JSONField(blank=True, null=True)),
                (
                    "document",
                    models.ForeignKey(
                        blank=True,
                        null=True,
                        on_delete=django.db.models.deletion.CASCADE,
                        to="cls_backend.document",
                    ),
                ),
            ],
            options={
                "abstract": False,
            },
        ),
        migrations.AddField(
            model_name="document",
            name="workspace",
            field=models.ForeignKey(
                blank=True,
                null=True,
                on_delete=django.db.models.deletion.PROTECT,
                to="cls_backend.workspace",
            ),
        ),
        migrations.CreateModel(
            name="Quota",
            fields=[
                (
                    "user",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.CASCADE,
                        primary_key=True,
                        serialize=False,
                        to=settings.AUTH_USER_MODEL,
                    ),
                ),
                ("created_time", models.DateTimeField(auto_now_add=True)),
                ("last_updated", models.DateTimeField(auto_now=True)),
                (
                    "package",
                    models.ForeignKey(
                        blank=True,
                        null=True,
                        on_delete=django.db.models.deletion.PROTECT,
                        related_name="user_quota",
                        to="cls_backend.package",
                    ),
                ),
                (
                    "roles",
                    models.ForeignKey(
                        blank=True,
                        null=True,
                        on_delete=django.db.models.deletion.PROTECT,
                        related_name="user_role",
                        to="cls_backend.role",
                    ),
                ),
            ],
        ),
    ]
