from typing import Dict, List, Any, <PERSON>ple
import cls_backend.utils.docx.config as cfg

PARA_CONTENT_T = List[Dict[str, Dict[str, Any]]]
TABLE_T = List[List[List[Dict[str, Any]]]]

def flatten_json(json: Dict[str, Any]) -> Dict[str, Any]:
    new_document = []
    for para in json["document"]:
        para_contents: PARA_CONTENT_T = para.get(cfg.paraContent, [])
        para_contents: str = flatten_para_content(para_contents)
        new_document.append({
            cfg.fileId: para.get(cfg.fileId),
            cfg.fileName: para.get(cfg.fileName),
            cfg.paraIndex: para.get(cfg.paraIndex),
            cfg.parentIndex: para.get(cfg.parentIndex),
            cfg.paraLevel: para.get(cfg.paraLevel),
            cfg.textValue: para_contents
        })
    return {"document": new_document}

def flatten_para_content(para_content: PARA_CONTENT_T) -> str:
    contents: List[str] = []
    next_first_heading: bool = True

    for p in para_content:
        if p.get(cfg.text):
            p_str: str = p.get(cfg.text, {}).get(cfg.textValue, "")
            contents.append(p_str)

        elif p.get(cfg.table):
            p_str, next_first_heading = flatten_table(p.get(cfg.table, []), next_first_heading)
            contents.append(p_str)

        elif p.get(cfg.image):
            descr: str = p.get(cfg.image, {}).get(cfg.imageDescription, "")
            target: str = p.get(cfg.image, {}).get(cfg.imageTarget, "")
            contents.append(f"{descr}: {target}".lstrip(": "))

    return " ".join(contents)

def flatten_table(table: TABLE_T, first_is_heading: bool = True) -> Tuple[str, bool]:
    headings: Dict[int, str] = {}
    heading_spans: Dict[int, bool] = {}

    contents: List[str] = []

    for i, row in enumerate(table):
        row_strs: List[Tuple[str]] = []

        for j, col in enumerate(row):
            span: bool = False
            col_strs: List[str] = []

            for cell in col:
                span = span or cell.get(cfg.span, False)
                col_str = concat_para_text(cell.get(cfg.paraContent, []))
                col_strs.append(col_str)

            col_strs: str = " ".join(col_strs).strip()

            if i == 0 and first_is_heading:
                headings[j] = col_strs

            elif first_is_heading:
                if heading_spans[j] or span:
                    if j not in headings:
                        headings[j] = ""
                    headings[j] += f" {col_strs}"
                    col_strs = ""
                    heading_spans[j] = False

            else:
                row_str = (headings.get(j, ""), col_strs)
                row_strs.append(row_str)

            heading_spans[j] = span

        if any(v[1] for v in row_strs):
            row_strs: str = "; ".join([": ".join(v).lstrip(": ") for v in row_strs])
            contents.append(row_strs)

    next_first_heading: bool = True

    if contents:
        return "\n".join(contents), next_first_heading

    return "; ".join(headings.values()), False

def concat_para_text(para_content: PARA_CONTENT_T) -> str:
    contents = []
    for p in para_content:
        contents.append(p.get(cfg.text, {}).get(cfg.textValue, ""))
    return "".join(contents)