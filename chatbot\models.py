from django.db import models
import json
import uuid
from cls_backend.models import User, WorkSpace
from chatbot.constants import *


class TimestampModel(models.Model):
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(null=True, blank=True)
    deleted_at = models.DateTimeField(null=True, blank=True)

    class Meta:
        abstract = True

class Conversation(TimestampModel):
    id = models.UUIDField(primary_key=True, default=uuid.uuid4, editable=False)
    workspace = models.ForeignKey(WorkSpace, on_delete=models.CASCADE)
    # user = models.ForeignKey(User, on_delete=models.CASCADE)
    name = models.CharField(max_length=255, default='Hỏi đáp pháp luật')

class ChatMessage(TimestampModel):
    LIKE = 'like'
    DISLIKE = 'dislike'
    UNLIKE = 'unlike'
    UNDISLIKE = 'undislike'
    MESSAGE_TYPES = [
        (USER_MESSAGE, 'user'),
        (LL<PERSON>_MESSAGE, 'assistant')
    ] 
    FEEDBACK_CHOICES = [
        (LIKE, 'Like'),
        (DISLIKE, 'Dislike'),
        (UNLIKE, 'Unlike'),
        (UNDISLIKE, 'Undislike')
    ]
    # user = models.CharField(max_length=255)
    id = models.UUIDField(primary_key=True, default=uuid.uuid4, editable=False)
    reply_to = models.ForeignKey(
        'self', on_delete=models.CASCADE, related_name="reply_to_message", null=True, blank=True
    )
    conversation = models.ForeignKey(Conversation, on_delete=models.CASCADE, null=True, blank=True)
    type = models.CharField(choices=MESSAGE_TYPES, default=USER_MESSAGE, max_length=25)
    thinking = models.TextField(null=True, blank=True)
    text = models.TextField()
    feedback = models.CharField(null=True, blank=True,choices=FEEDBACK_CHOICES, max_length=10)
    def as_json(self):
        return json.dumps(
            {
                "id": str(self.id),
                "conversation_id": str(self.conversation_id),
                "text": self.text,
            },
        )