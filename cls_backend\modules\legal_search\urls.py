from rest_framework.routers import De<PERSON>ult<PERSON><PERSON><PERSON>
from django.urls import include, path
from .views import *

urlpatterns = [
    path('by_query', LegalSearchByQueryView.as_view()),
    path('by_query/<uuid:document_id>', LegalSearchByQueryView.as_view(), name='legal-search-put'),
    path('history', LegalSearchHistoryView.as_view()),
    path('summary', LegalSearchSummaryView.as_view()),
    path('suggest', LegalSearchSuggesterView.as_view()),
    path('save_history-search', HistorySearchResultView.as_view(), name='history-search'),
    path('save_history-search/<int:history_id>', HistorySearchResultDetailView.as_view(), name='history-search-delete'),
    path('save_history-search/search', HistorySearchResultViewSet.as_view({'get': 'search'}), name='history-search-search'),
]

