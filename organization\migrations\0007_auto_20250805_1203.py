# Generated by Django 4.2 on 2025-08-05 05:03

from django.db import migrations


def set_default_organization(apps, schema_editor):
    """Set existing 'Default Organization' as is_default=True"""
    Organization = apps.get_model('organization', 'Organization')

    # Tìm organization có tên "Default Organization" và set is_default=True
    default_orgs = Organization.objects.filter(name__icontains='Default Organization')
    if default_orgs.exists():
        # Chỉ set organization đầu tiên tìm thấy
        default_org = default_orgs.first()
        default_org.is_default = True
        default_org.save()
    else:
        # Nếu không tìm thấy, tạo mới
        Organization.objects.create(
            name='Default Organization',
            description='Tổ chức mặc định cho các tài khoản mới',
            status='active',
            is_default=True
        )


def reverse_set_default_organization(apps, schema_editor):
    """Reverse the migration"""
    Organization = apps.get_model('organization', 'Organization')
    Organization.objects.filter(is_default=True).update(is_default=False)


class Migration(migrations.Migration):

    dependencies = [
        ('organization', '0006_organization_is_default'),
    ]

    operations = [
        migrations.RunPython(set_default_organization, reverse_set_default_organization),
    ]
