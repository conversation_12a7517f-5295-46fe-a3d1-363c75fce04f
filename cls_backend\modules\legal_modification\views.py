import json

import requests
from decouple import config
from rest_framework import status
from rest_framework.permissions import IsAuthenticated
from rest_framework.response import Response
from rest_framework.views import APIView

from cls_backend.constants import REQUEST_TIME_OUT
from cls_backend.modules.keywords.views import LoaiVanBanView


class NormSearchView(APIView):
    permission_classes = [IsAuthenticated]

    def post(self, request):
        data = json.loads(request.body)
        try:
            url = config('CLS_HOST') + '/legal_modification/norms/search'
            headers = {
                'Content-Type': 'application/json'
            }

            response = requests.request("POST", url, headers=headers, data=json.dumps(data), timeout=REQUEST_TIME_OUT)
            return Response(response.json(), status=response.status_code)
        except:
            return Response({}, status=status.HTTP_500_INTERNAL_SERVER_ERROR)


# class DocumentSearchView(APIView):
#     permission_classes = [IsAuthenticated]

#     def post(self, request):
#         data = json.loads(request.body)
#         try:
#             url = config('CLS_HOST') + '/legal_modification/documents/search'
#             headers = {
#                 'Content-Type': 'application/json'
#             }

#             response = requests.request("POST", url, headers=headers, data=json.dumps(data), timeout=REQUEST_TIME_OUT)
#             return Response(response.json(), status=response.status_code)
#         except:
#             return Response({}, status=status.HTTP_500_INTERNAL_SERVER_ERROR)
class DocumentSearchView(APIView):
    permission_classes = [IsAuthenticated]

    def post(self, request):
        data = json.loads(request.body)
        try:
            # Gọi API tìm kiếm tài liệu
            url = config('CLS_HOST') + '/legal_modification/documents/search'
            headers = {
                'Content-Type': 'application/json'
            }
            response = requests.request("POST", url, headers=headers, data=json.dumps(data), timeout=REQUEST_TIME_OUT)
            response_data = response.json()

            loai_van_ban_view = LoaiVanBanView()
            loai_van_ban_response = loai_van_ban_view.get(request)
            loai_van_ban_list = loai_van_ban_response.data 

            statistic = {item['label']: 0 for item in loai_van_ban_list}

            documents = response_data.get("data", [])
            for doc in documents:
                loai_van_ban = doc.get("loai_van_ban", "")
                if loai_van_ban in statistic:
                    statistic[loai_van_ban] += 1

            statistic_list = [{"label": k, "count": v} for k, v in statistic.items() if v > 0]

            # Thêm statistic vào response
            response_data["statistic"] = statistic_list

            return Response(response_data, status=response.status_code)
        except Exception as e:
            print(f"Error: {e}")
            return Response({}, status=status.HTTP_500_INTERNAL_SERVER_ERROR)

class NormCheckView(APIView):
    permission_classes = [IsAuthenticated]

    def post(self, request):
        data = json.loads(request.body)
        try:
            url = config('CLS_HOST') + '/legal_modification/norms/check'
            headers = {
                'Content-Type': 'application/json'
            }

            response = requests.request("POST", url, headers=headers, data=json.dumps(data), timeout=REQUEST_TIME_OUT)
            return Response(response.json(), status=response.status_code)
        except:
            return Response({}, status=status.HTTP_500_INTERNAL_SERVER_ERROR)


class DocumentCheckView(APIView):
    permission_classes = [IsAuthenticated]

    def post(self, request):
        data = json.loads(request.body)
        try:
            url = config('CLS_HOST') + '/legal_modification/documents/check'
            headers = {
                'Content-Type': 'application/json'
            }

            response = requests.request("POST", url, headers=headers, data=json.dumps(data), timeout=REQUEST_TIME_OUT)
            return Response(response.json(), status=response.status_code)
        except:
            return Response({}, status=status.HTTP_500_INTERNAL_SERVER_ERROR)
