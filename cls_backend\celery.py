import os

from celery import Celery
from kombu import Queue
import django



os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'cls_backend.settings')
django.setup()
from cls_backend.tasks import *

# Set the default Django settings module for the 'celery' program.

app = Celery('cls')

app.conf.task_queues = (
    Queue('default', routing_key='default'),
    Queue('ocr', routing_key='ocr'),
)
app.conf.task_default_queue = 'default'
app.conf.task_default_exchange = 'default'
app.conf.task_default_exchange_type = 'direct'
app.conf.task_default_routing_key = 'default'

# Using a string here means the worker doesn't have to serialize
# the configuration object to child processes.
# - namespace='CELERY' means all celery-related configuration keys
#   should have a `CELERY_` prefix.
app.config_from_object('django.conf:settings', namespace='CELERY')

app.task(name='handle_conflict')(handle_conflict)
app.task(name='term_review')(term_review)
app.task(name='handle_ocr')(handle_ocr)
app.task(name='handle_ie')(handle_ie)
app.conf.task_routes = {
    'handle_conflict': {'queue': 'default'},
    'term_review': {'queue': 'default'},
    'handle_ocr': {'queue': 'ocr'},
    'handle_ie': {'queue': 'ocr'},
}


# Load task modules from all registered Django apps.
# app.autodiscover_tasks()
