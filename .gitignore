*.pot

*.pyc
launch.json

# User-specific stuff:
**/.idea/workspace.xml
**/.idea/tasks.xml
**/.idea/dictionaries
**/.idea/vcs.xml
**/.idea/jsLibraryMappings.xml
.idea/*

# Sensitive or high-churn files:
**/.idea/dataSources.ids
**/.idea/dataSources.xml
**/.idea/dataSources.local.xml
**/.idea/sqlDataSources.xml
**/.idea/dynamic.xml
**/.idea/uiDesigner.xml

*/*/*/*/__pycache__/
*/*/*/__pycache__/

*/*/__pycache__/

*/__pycache__/


list_url_already_crawl.txt
dbs/*
eggs/*
server/db.sqlite3
server/server/.env


# Selenium
selenium/chromecache/
db.sqlite3
/staticfiles
docker-compose.yaml

image_temp/
media/*
backend/conf/config.py
*.sqlite3

test1.py
.env
.vs
locale
venv
private_media

.idea/

statistics/
logs/*

.venv/
schema.mwb.bak