import json

import requests
from decouple import config
from django.db.models import Q
from rest_framework import status
from rest_framework.decorators import action
from rest_framework.generics import RetrieveAPIView, CreateAPIView, ListAPIView
from rest_framework.permissions import IsAuthenticated
from rest_framework.response import Response
from rest_framework.views import APIView
from rest_framework.viewsets import GenericViewSet

from cls_backend.constants import REQUEST_TIME_OUT
from cls_backend.constants import STATUS_FAILED, STATUS_PENDING, STATUS_SUCCESS, STATUS_PROCESSING
from cls_backend.modules.conflict.serializers import *
from cls_backend.modules.document.serializers import DocumentCompareSerializer
from cls_backend.tasks import handle_ie
from cls_backend.tasks.handle_conflict import handle_conflict
from django.shortcuts import get_object_or_404
import logging


logger = logging.getLogger("cls")


class SearchEffectiveTimeView(APIView):
    permission_classes = [IsAuthenticated]
    serializer_class = DocumentCompareSerializer

    def post(self, request):
        data = json.loads(request.body)
        payload = json.dumps(data)
        ai_url = config('CLS_HOST') + config('SEARCH_EFFECTIVE_TIME')
        try:
            response = requests.post(ai_url, data=payload, timeout=REQUEST_TIME_OUT)
            return Response(response.json(), status=response.status_code)
        except:
            return Response({}, status=status.HTTP_500_INTERNAL_SERVER_ERROR)


class CheckEffectiveTimeView(APIView):
    permission_classes = [IsAuthenticated]
    serializer_class = DocumentCompareSerializer

    def post(self, request):
        data = json.loads(request.body)
        payload = json.dumps(data)
        ai_url = config('CLS_HOST') + config('CHECK_EFFECTIVE_TIME')
        try:
            response = requests.post(ai_url, data=payload, timeout=REQUEST_TIME_OUT)
            return Response(response.json(), status=response.status_code)
        except:
            return Response({}, status=status.HTTP_500_INTERNAL_SERVER_ERROR)


class AuthorityViewSet(ListAPIView, RetrieveAPIView, CreateAPIView, GenericViewSet):
    permission_classes = [IsAuthenticated]
    serializer_class = DocumentAuthoritySerializer
    query_set = Document.objects.filter(deleted_at=None)

    # def get_queryset(self):
    #     request = self.request
    #     user = self.request.user
    #     keyword = request.query_params.get('keyword')
    #     query_set = self.query_set
    #     if not user.is_superuser:
    #         query_set = query_set.filter(created_by=user)
    #     if keyword and len(keyword) > 0:
    #         query_set = query_set.filter(name__icontains=keyword)
    #     # query_set = query_set.exclude(
    #     #     # Q(is_convert=True) 
    #     #     # & 
    #     #     Q(status__in=[
    #     #         STATUS_FAILED,
    #     #         STATUS_PROCESSING,
    #     #         STATUS_PENDING
    #     #     ])
    #     # )
    #     query_set = query_set.defer('json_data')
    #     return query_set.order_by('-id')

    # def create(self, request, *args, **kwargs):
    #     data = request.data
    #     file_serializer = self.serializer_class(data=data)
    #     file_serializer.is_valid(raise_exception=True)
    #     # file_serializer.save(
    #     #     created_by=request.user, 
    #     #     name=data.get('origin').name, 
    #     #     authority_status=STATUS_PENDING
    #     # )
    #     document_data = {
    #         'created_by': request.user, 
    #         'name': data.get('origin').name, 
    #         'authority_status': STATUS_PENDING
    #     }
    #     if data.get('origin').name.endswith('.docx'):
    #         document_data['status'] = STATUS_SUCCESS
    #     file_serializer.save(**document_data)
        
    #     logger.debug("start handle conflict")
    #     logger.debug(file_serializer.data['id'])
    #     handle_conflict.delay(document_id=file_serializer.data['id'])
    #     # logger.debug("######")
    #     return Response(file_serializer.data, status=status.HTTP_201_CREATED)

    @action(methods=['post'], detail=True)
    def rerun(self, request, *args, **kwargs):
        document_id = self.kwargs['pk']

        document = Document.objects.get(pk=document_id)
        document.authority_status = STATUS_PENDING
        document.save(update_fields=['authority_status'])

        # filters = {'id': document_id, 'deleted_at': None}
        # if not self.request.user.is_superuser:
        #     filters['created_by'] = self.request.user
        # document = get_object_or_404(Document, **filters)
        handle_ie.apply_async(kwargs={'document_id': document.id}, queue='ocr')
        handle_conflict.delay(document_id=document_id)

        return Response(status=status.HTTP_200_OK)


class AuthorityHistoryViewSet(ListAPIView, RetrieveAPIView, CreateAPIView, GenericViewSet):
    permission_classes = [IsAuthenticated]
    serializer_class = DocumentAuthorityLessInfoSerializer

    def get_queryset(self):
        request = self.request
        user = request.user
        keyword = request.query_params.get('keyword')
        query_set = DocumentAuthority.objects.select_related('document') \
            .only('document__name', 'document__origin', 'document__id', 'document__created_by',
                  'document__authority_status') \
            .filter(deleted_at=None, document__authority_status=STATUS_SUCCESS)

        if not user.is_superuser:
            query_set = query_set.filter(document__created_by=user)
        if keyword and len(keyword) > 0:
            query_set = query_set.filter(document__name__icontains=keyword)
        query_set = query_set.defer('noi_dung', 'hinh_thuc')
        return query_set.order_by('-id')

    def retrieve(self, request, *args, **kwargs):
        document_id = self.kwargs['pk']
        filters = {'document__id': document_id}

        if not self.request.user.is_superuser:
            filters['document__created_by'] = self.request.user

        document_authority = get_object_or_404(DocumentAuthority, **filters)
        serializer = RetrieveDocumentAuthoritySerializer(document_authority)

        return Response(serializer.data, status=status.HTTP_200_OK)
