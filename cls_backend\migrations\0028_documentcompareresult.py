# Generated by Django 4.2 on 2025-07-17 08:28

from django.db import migrations, models
import django.db.models.deletion
import uuid


class Migration(migrations.Migration):

    dependencies = [
        ('cls_backend', '0027_documentsummarize_status'),
    ]

    operations = [
        migrations.CreateModel(
            name='DocumentCompareResult',
            fields=[
                ('id', models.UUIDField(default=uuid.uuid4, editable=False, primary_key=True, serialize=False)),
                ('old_content', models.JSONField(blank=True, null=True)),
                ('new_content', models.JSONField(blank=True, null=True)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
                ('document_new', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.CASCADE, related_name='compare_results_as_new', to='cls_backend.document')),
                ('document_old', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.CASCADE, related_name='compare_results_as_old', to='cls_backend.document')),
            ],
        ),
    ]
