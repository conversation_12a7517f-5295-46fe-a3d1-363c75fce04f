import json
import os
import tempfile

from django.http import HttpResponse
from django.shortcuts import get_object_or_404
import requests
from decouple import config
from django.db.models import Q
from rest_framework import status
from rest_framework.decorators import action
from rest_framework.permissions import IsAuthenticated
from rest_framework.response import Response
from rest_framework.generics import RetrieveAPIView, CreateAPIView, ListAPIView
from rest_framework.viewsets import GenericViewSet
from chatbot.constants import LLM_MESSAGE
from chatbot.models import Conversation, ChatMessage
from chatbot.serializers import ConversationSerializer, ChatMessageSerializer
from datetime import datetime
from cls_backend.models import WorkSpace

from gtts import gTTS
from django.http import FileResponse


class ConversationViewSet(ListAPIView, RetrieveAPIView, CreateAPIView, GenericViewSet):
    permission_classes = [IsAuthenticated]
    serializer_class = ConversationSerializer
    query_set = Conversation.objects.filter(deleted_at=None)

    def get_queryset(self):
        request = self.request
        keyword = request.query_params.get('keyword')
        workspace_id = request.query_params.get('workspace_id')
        query_set = self.query_set
        # Nếu workspace_id rỗng, tự động tìm hoặc tạo workspace is_no_folder cho user
        if not workspace_id:
            user = request.user
            workspace = WorkSpace.objects.filter(user=user, is_no_folder=True, is_deleted=False).first()
            if not workspace:
                workspace = WorkSpace.objects.create(
                    user=user,
                    name="Không phân loại",
                    description="Workspace mặc định không phân loại",
                    is_no_folder=True
                )
            workspace_id = workspace.id
        query_set = query_set.filter(workspace_id=workspace_id)
        if keyword and len(keyword) > 0:
            query_set = query_set.filter(name__icontains=keyword)
        return query_set.order_by('-updated_at')

    # def create(self, request, *args, **kwargs):
    #     data = request.data
    #     serializer = self.serializer_class(data=data)
    #     serializer.is_valid(raise_exception=True)
    #     serializer.save()
    #     return Response(serializer.data, status=status.HTTP_201_CREATED)

    def list(self, request):
        workspace_id = request.query_params.get('workspace_id')
        query = Conversation.objects.all()
        if workspace_id:
            query = query.filter(workspace_id=workspace_id)
        query = query.order_by('-updated_at')
        serializer = self.serializer_class(query, many=True)
        return Response(serializer.data)

    def retrieve(self, request, pk):
        instance = Conversation.objects.get(id=pk)
        serializer = self.serializer_class(instance)
        data = serializer.data
        message_queryset = ChatMessage.objects.filter(conversation=instance).order_by('created_at')
        data['messages'] = ChatMessageSerializer(message_queryset, many=True).data
        return Response(data)

    def update(self, request, pk):
        instance = Conversation.objects.get(id=pk)
        data = request.data.copy()
        data['updated_at'] = datetime.now()
        serializer = self.serializer_class(instance, data=data, partial=True)
        serializer.is_valid(raise_exception=True)
        serializer.save()
        return Response(serializer.data)

    def destroy(self, request, pk):
        instance = Conversation.objects.get(id=pk)
        instance.deleted_at = datetime.now()
        instance.save()
        return Response(status=status.HTTP_204_NO_CONTENT)
    
    @action(methods=['post'], detail=False, url_path='message_feedback', permission_classes=[IsAuthenticated])
    def message_feedback(self, request):
        message_id = request.data.get('message_id')
        feedback = request.data.get('feedback')
        if feedback not in [ChatMessage.LIKE, ChatMessage.DISLIKE, ChatMessage.UNLIKE, ChatMessage.UNDISLIKE]:
            return Response({'detail': 'Invalid feedback value.'}, status=status.HTTP_400_BAD_REQUEST)
        message = get_object_or_404(ChatMessage, id=message_id)
        message.feedback = feedback
        message.save(update_fields=['feedback', 'updated_at'])
        return Response({'detail': 'Feedback updated successfully.', 'message_id': str(message.id), 'feedback': message.feedback})
    
    @action(methods=['get'], detail=False, url_path='message_tts/(?P<message_id>[^/.]+)', permission_classes=[IsAuthenticated])
    def message_tts(self, request, message_id=None):
        message = get_object_or_404(ChatMessage, pk=message_id, type=LLM_MESSAGE)

        # Tạo file tạm với delete=False
        with tempfile.NamedTemporaryFile(delete=False, suffix=".mp3") as temp_file:
            temp_path = temp_file.name  # lưu lại đường dẫn
        try:
            # Ghi file bằng gTTS
            tts = gTTS(text=message.text, lang='vi')
            tts.save(temp_path)

            # Đọc file và trả về
            with open(temp_path, 'rb') as f:
                response = HttpResponse(f.read(), content_type='audio/mpeg')
                response['Content-Disposition'] = f'attachment; filename="{message_id}.mp3"'
                return response
        finally:
            # Xóa file sau khi trả về
            if os.path.exists(temp_path):
                os.remove(temp_path)