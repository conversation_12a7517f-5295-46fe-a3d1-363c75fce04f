from celery import shared_task
from django.db import transaction
from cls_backend.constants import (
    STATUS_IE, STATUS_PROCESSING, STATUS_FAILED, STATUS_SUCCESS, STATUS_NOTFOUND, STATUS_CONFLICT, TASK_TIMEOUT,
    VAN_BAN_CAN_CU, VAN_BAN_LIEN_QUAN_KHAC
)
from cls_backend.models import Document, DocumentAuthority, DocumentCompare, Law<PERSON>lause, DocumentRelated
from cls_backend.tasks.handle_ie import HARD_TASK_TIMEOUT, SOFT_TASK_TIMEOUT
import logging
import uuid
from cls_backend.utils.handle_file import RedisUtil
from cls_backend.modules.notification.event import EVENT_CONFLICT_DOING, EVENT_IE_DOING, EVENT_TQND_DONE
from decouple import config
import json
import requests
import time
from cls_backend.modules.notification.utils import send_notifications
from datetime import datetime
from celery.exceptions import SoftTimeLimitExceeded

from cls_backend.utils.province_convert import convert_agency_name
from cls_backend.utils.timeout_terminate_celery import BaseTask

logger = logging.getLogger("cls")

@shared_task(bind=True, base=BaseTask, time_limit=HARD_TASK_TIMEOUT, soft_time_limit=SOFT_TASK_TIMEOUT)
def handle_conflict(self,document_id):
    logger.debug(f"Start handling conflict document id {document_id}")
    document = Document.objects.get(pk=document_id)

    document.authority_status = STATUS_PROCESSING
    document.compare_status = STATUS_PROCESSING
    document.status = STATUS_CONFLICT
    # if document.ie_done:
    #     document.percentage = 75
    # else:
    #     document.percentage = 50
    document.save(update_fields=['authority_status', 'compare_status', 'status'])
    # send_notifications(
    #         user_id=document.created_by_id,
    #         event=EVENT_CONFLICT_DOING,
    #         data={"name": document.name, "status": STATUS_CONFLICT, "document_id":str(document.id)},
    #         percentage=75 if document.ie_done else 50
    #     )
    try:
        # type_and_issuing_agency = extract_category(document.json_data)

        task_uuid = str(document.id)
        # task_uuid = '123456abc'

        # if type_and_issuing_agency:

        # To assure that DocumentAuthority is created before runnign sub tasks
        query = DocumentAuthority.objects.filter(document=document)
        if not query.exists():
            with transaction.atomic():
                doc_authority = DocumentAuthority.objects.create(document=document)
        else:
            doc_authority = query.first()


        # URL = config('CLS_HOST') + '/v2/analyze'
        URL = config('CLS_HOST') + '/v2/legal_doc_analyze'
        params = {
            'id': str(document.id),
            'uuid': document.id,
            # 'user_id': document.created_by_id
        }
        time.sleep(5)
        
        files=[
            (
                'file', 
                (
                    document.name, 
                    document.convert.open('rb') if document.is_convert else document.origin.open('rb'), 
                    'application/vnd.openxmlformats-officedocument.wordprocessingml.document'
                )
            )
        ]

        # logger.debug(payload)
        # logger.debug(files)
        response = requests.post(URL, data=params, files=files)

        if response.status_code == 201:
            document.authority_status != STATUS_NOTFOUND
            document.save(update_fields=['authority_status'])
            return
        elif response.status_code != 200:
            logger.error(response.text)
            raise Exception(response.text)
        
        doi_chieu_key = task_uuid
        hinh_thuc_key = f"{task_uuid}_hinh_thuc"
        noi_dung_key = f"{task_uuid}_noi_dung"
        hieu_luc_key = f"{task_uuid}_hieu_luc"

        r = RedisUtil.get_instance('celery')
        time_start = time.time()
        while time.time()-time_start<SOFT_TASK_TIMEOUT:
            logger.debug("Waiting for task result")
            if r._redis_client.exists(doi_chieu_key) and document.compare_status != STATUS_SUCCESS:
                logger.debug("Task compare is success")
                result = r._redis_client.get(doi_chieu_key)
                result = json.loads(result)
                # logger.debug(result)
                process_compare_result(document, result)
                document.compare_status = STATUS_SUCCESS
                document.save(update_fields=['compare_status'])
            if r._redis_client.exists(hinh_thuc_key) and not document.hinh_thuc_done:
                logger.debug("Task hinh thuc is success")
                result = r._redis_client.get(hinh_thuc_key)
                result = json.loads(result)
                doc_authority.hinh_thuc = result
                doc_authority.save(update_fields=['hinh_thuc'])
                document.hinh_thuc_done = True
            if r._redis_client.exists(noi_dung_key) and not document.noi_dung_done:
                logger.debug("Task noi dung is success")
                result = r._redis_client.get(noi_dung_key)
                result = json.loads(result)
                doc_authority.noi_dung = result
                doc_authority.save(update_fields=['noi_dung'])
                document.noi_dung_done = True
            if r._redis_client.exists(hieu_luc_key) and not document.hieu_luc_done:
                logger.debug("Task hieu luc is success")
                result = r._redis_client.get(hieu_luc_key)
                result = json.loads(result)
                doc_authority.hieu_luc = result
                doc_authority.save(update_fields=['hieu_luc'])
                document.hieu_luc_done = True
            
            if document.noi_dung_done and document.hinh_thuc_done and document.hieu_luc_done:
                document.authority_status = STATUS_SUCCESS
                document.save(
                    update_fields=[
                        'hinh_thuc_done',
                        'noi_dung_done',
                        'hieu_luc_done',
                        'authority_status'
                    ]
                )
            doc = Document.objects.get(pk=document_id)
            if document.compare_status == STATUS_SUCCESS and document.authority_status == STATUS_SUCCESS and not doc.ie_done:
                document.percentage = 75
                document.save(update_fields=['percentage'])
                # logger.debug("All task is finished")
                send_notifications(
                    user_id=document.created_by_id, 
                    event=EVENT_IE_DOING, 
                    data={
                        "name": document.name,
                        "status": STATUS_IE,
                        "document_id":str(document.id)
                    },
                    percentage=75
                )
                return
            if document.compare_status == STATUS_SUCCESS and document.authority_status == STATUS_SUCCESS and doc.ie_done:
                document.status = STATUS_SUCCESS
                document.percentage = 100
                document.save(update_fields=['status','percentage'])
                logger.debug("All task is finished")
                send_notifications(
                    user_id=document.created_by_id, 
                    event=EVENT_TQND_DONE, 
                    data={
                        "name": document.name,
                        "status": STATUS_SUCCESS,
                        "document_id":str(document.id)
                    },
                    percentage=100
                )
                return
            
            time.sleep(10)

        # else:
        #     document.authority_status = STATUS_FAILED
        #     document.save(update_fields=['authority_status'])
        #     logger.debug("extract_category ERROR")
        raise TimeoutError(f'Timeout exceed {SOFT_TASK_TIMEOUT}')
    except SoftTimeLimitExceeded:
        # Bắt sự kiện timeout tại đây
        logger.error(f"Task handle_ie timeout for document {document_id}.")
        self.on_timeout(self.request.id, args=(document_id,), kwargs={})
        document.status = STATUS_FAILED
        document.save(update_fields=['status'])
        send_notifications(
                user_id=document.created_by_id,
                event=EVENT_TQND_DONE,
                data={"name": document.name, "status": STATUS_FAILED,"document_id":str(document.id)}
            )
    except Exception as e:
        if "no file associated" in str(e):
            logger.warning(f"File convert chưa sẵn sàng cho document {document_id}. Không gửi failed, sẽ retry.")
            raise self.retry(countdown=5, exc=e, max_retries=5)
        else:
            logger.exception(e)
            document.status = STATUS_FAILED
            document.save(update_fields=['status'])
            send_notifications(
                    user_id=document.created_by_id,
                    event=EVENT_TQND_DONE,
                    data={"name": document.name, "status": STATUS_FAILED,"document_id":str(document.id)}
                )
            raise e
    
def process_compare_result(document, result):
    if document.compare_status ==STATUS_SUCCESS:
        return
    
    try:
        document_compare, _ = DocumentCompare.objects.update_or_create(
            document=document,
            defaults={
                'document_based': result['can_cu'],
                'document_related': result['lien_quan'],
                "dieu_khoan": result['dieu_khoan']
            },
        )
        document.toan_van = result['toan_van_html']
        document.save(update_fields=['toan_van'])

        # logger.debug(result)
        for related_type, related_documents in [
            (VAN_BAN_CAN_CU, result['can_cu']['data']),
            (VAN_BAN_LIEN_QUAN_KHAC, result['lien_quan']['data'])
        ]:
            # logger.debug([related_type, related_documents])
            for related_doc in related_documents:
                # logger.debug(related_doc)
                logger.debug(related_doc)
                don_vi = related_doc.get('don_vi', [])
                if len(don_vi)==0:
                    don_vi = []
                DocumentRelated.objects.create(
                    document=document,
                    relation_type=related_type,
                    ID=related_doc.get('ID', None) or related_doc.get('id', None),
                    vbpl_id=related_doc.get('vbpl_id', None),
                    url=related_doc.get('url', None),
                    score=related_doc.get('score', None),
                    title=related_doc.get('title', None),
                    toan_van=related_doc.get('toan_van', None),
                    don_vi=don_vi,
                    so_hieu=related_doc.get('so_hieu', None),
                    nguoi_ky=related_doc.get('nguoi_ky', None),
                    trich_yeu=related_doc.get('trich_yeu', None),
                    loai_van_ban=related_doc.get('loai_van_ban', None),
                    ngay_ban_hanh=related_doc.get('ngay_ban_hanh', None),
                    co_quan_ban_hanh=convert_agency_name(related_doc.get('co_quan_ban_hanh')),
                    ngay_co_hieu_luc=related_doc.get('ngay_co_hieu_luc', None),
                    tinh_trang_hieu_luc=related_doc.get('tinh_trang_hieu_luc', None),
                    noi_dung=related_doc.get('noi_dung', None)
                )

        LawClause.objects.filter(document=document).update(deleted_at=datetime.now())
        for dieu_khoan in result['dieu_khoan']['data']:
            LawClause.objects.create(
                document=document,
                clause_id=dieu_khoan.get('id', None),
                title=dieu_khoan.get('title', None),
                content=dieu_khoan.get('content', None),
                position=dieu_khoan.get('position', None),
                show_content=dieu_khoan.get('show_content', None),
                doc_id=dieu_khoan.get('doc_id', None),
                is_raw=dieu_khoan.get('is_raw', False),
                order = dieu_khoan.get('index', 0)
            )
    except Exception as e:
        logger.exception(e)
        document.status = STATUS_FAILED
        document.save(update_fields=['status'])
        raise e
