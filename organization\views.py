# Create your views here.
from rest_framework import viewsets, status
from rest_framework.permissions import IsAuthenticated
from rest_framework.response import Response
from rest_framework.decorators import action
from django.shortcuts import get_object_or_404

from .models import Organization, OrganizationMember
from .serializers import OrganizationSerializer
from cls_backend.models import User
from organization.organization_member.serializers import OrganizationMemberSerializer

class OrganizationViewSet(viewsets.ModelViewSet):
    queryset = Organization.objects.all()
    permission_classes = [IsAuthenticated]
    serializer_class = OrganizationSerializer

    @action(detail=True, methods=['POST'], url_path='massive-add-users')
    def massive_add_users(self, request, pk=None):
        """
        Add multiple users to an organization by username pattern.
        Expects JSON: { "username_pattern": "prefix*" }
        """
        username_pattern = request.data.get('username_pattern')
        role = request.data.get('role', 'member')
        if not username_pattern:
            return Response({'detail': 'username_pattern is required.'}, status=status.HTTP_400_BAD_REQUEST)
        organization = get_object_or_404(Organization, pk=pk)

        # Convert pattern to Django __startswith or __icontains
        if username_pattern.endswith('*'):
            pattern = username_pattern[:-1]
            users = User.objects.filter(email__startswith=pattern)
        elif '*' in username_pattern:
            pattern = username_pattern.replace('*', '')
            users = User.objects.filter(email__icontains=pattern)
        else:
            users = User.objects.filter(email=username_pattern)

        if not users.exists():
            return Response({'detail': 'No users found matching the pattern.'}, status=status.HTTP_404_NOT_FOUND)

        added = []
        skipped = []
        for user in users:
            # Check if already a member
            if OrganizationMember.objects.filter(organization=organization, user=user).exists():
                skipped.append(user.email)
                continue
            OrganizationMember.objects.create(
                organization=organization,
                user=user,
                role=role
            )
            added.append(user.email)

        return Response({
            'total_added': len(added),
            'total_skipped': len(skipped)
        }, status=status.HTTP_200_OK)
    
    @action(detail=True, methods=['GET'], url_path='users')
    def users(self, request, pk=None):
        members = OrganizationMember.objects.filter(organization_id=pk).select_related('user')
        queryset = self.paginate_queryset(members)
        serializer = OrganizationMemberSerializer(queryset, many=True)
        return self.get_paginated_response(serializer.data)
