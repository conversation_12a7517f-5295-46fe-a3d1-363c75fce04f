import json

import requests
from decouple import config
from django.core.exceptions import ObjectDoesNotExist
from rest_framework import status
from rest_framework.views import APIView
from rest_framework.permissions import IsAuthenticated
from rest_framework.response import Response

from cls_backend.modules.document.serializers import *
from cls_backend.modules.document.utils import compare_ai, find_document_ai

from cls_backend.constants import REQUEST_TIME_OUT

class LoaiVanBanView(APIView):
    permission_classes = [IsAuthenticated]

    def get(self, request):
        try:
            url = config('CLS_HOST') + config('KEYWORDS_LOAI_VAN_BAN')
            headers = {
                'Content-Type': 'application/json'
            }

            response = requests.get(url, headers=headers)
            return Response(response.json(), status=response.status_code)
        except Exception as e:
            print(e)
            return Response({}, status=status.HTTP_500_INTERNAL_SERVER_ERROR)


class CoQuanBanHanhView(APIView):
    permission_classes = [IsAuthenticated]

    def get(self, request):
        try:
            url = config('CLS_HOST') + config('KEYWORDS_CO_QUAN_BAN_HANH')
            headers = {
                'Content-Type': 'application/json'
            }

            response = requests.get(url, headers=headers)
            return Response(response.json(), status=response.status_code)
        except:
            return Response({}, status=status.HTTP_500_INTERNAL_SERVER_ERROR)


class DomainsView(APIView):
    permission_classes = [IsAuthenticated]

    def get(self, request):
        try:
            url = config('CLS_HOST') + config('KEYWORDS_CHU_DE_PHAP_DIEN')
            headers = {
                'Content-Type': 'application/json'
            }

            response = requests.get(url, headers=headers)
            return Response(response.json(), status=response.status_code)
        except:
            return Response({}, status=status.HTTP_500_INTERNAL_SERVER_ERROR)


class SuaDoiBoSungView(APIView):
    permission_classes = [IsAuthenticated]

    def get(self, request):
        try:
            url = config('CLS_HOST') + config('KEYWORDS_SUA_DOI_BO_SUNG')
            headers = {
                'Content-Type': 'application/json'
            }

            response = requests.get(url, headers=headers)
            return Response(response.json(), status=response.status_code)
        except:
            return Response({}, status=status.HTTP_500_INTERNAL_SERVER_ERROR)


class TrangThaiHieuLucView(APIView):
    permission_classes = [IsAuthenticated]

    def get(self, request):
        try:
            url = config('CLS_HOST') + '/utils/keywords/tinh_trang_hieu_luc'
            headers = {
                'Content-Type': 'application/json'
            }

            response = requests.get(url, headers=headers)
            return Response(response.json(), status=response.status_code)
        except:
            return Response({}, status=status.HTTP_500_INTERNAL_SERVER_ERROR)

class FilterKeywordView(APIView):
    permission_classes = [IsAuthenticated]

    def get(self, request):
        data = {
            "loai_van_ban": request.GET.get('loai_van_ban', "") ,
            "co_quan_ban_hanh": request.GET.get('co_quan_ban_hanh', ""),
            "tinh_trang_hieu_luc": request.GET.get('tinh_trang_hieu_luc', "")
        }
        try:
            url = config('CLS_HOST') + '/utils/keywords/filter'
            headers = {
                'Content-Type': 'application/json'
            }
            response = requests.get(url, headers=headers, params=data, timeout=REQUEST_TIME_OUT )
            return Response(response.json(), status=response.status_code)
        except:
            return Response({}, status=status.HTTP_500_INTERNAL_SERVER_ERROR)
        
