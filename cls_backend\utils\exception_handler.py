# custom handler
from rest_framework.views import exception_handler

def custom_exception_handler(exc, context):
    # Call REST framework's default exception handler first,
    # to get the standard error response.
    response = exception_handler(exc, context)
    # Now add the HTTP status code to the response.
    # Update the structure of the response data.
    if response is not None:
        customized_response = {}
        if isinstance(response.data,list):
            for index in range(len(response.data)):
                customized_response[index] = {
                    'error': []
                }
                for key, value in response.data[index].items():
                    error = f"{value[0]}" if isinstance(value,list) else f"{value}"
                    customized_response[index]['error'].append(error)
                customized_response[index]['error'] = ' '.join(customized_response[index]['error'])
                response.data[index] = customized_response[index]
        else:
            customized_response['error'] = []
            for key, value in response.data.items():
                error = f"{value[0]}" if isinstance(value,list) else f"{value}"
                customized_response['error'].append(error)
            customized_response['error'] = ' '.join(customized_response['error'])
            response.data = customized_response

    return response