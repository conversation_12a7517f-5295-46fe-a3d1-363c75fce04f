# pull official base image
FROM python:3.8-slim-bullseye
# install psycopg2 dependencies
# RUN apt-get update \
#     && apt-get install -y gcc \
#     libpq-dev \
#     python3-dev \
#     musl-dev \
#     && rm -rf /var/lib/apt/lists/*

## virtualenv
ENV VIRTUAL_ENV=/opt/venv
RUN python3 -m venv $VIRTUAL_ENV
ENV PATH="$VIRTUAL_ENV/bin:$PATH"


# FROM python:3.8-slim-bullseye AS final

# install psycopg2 dependencies
RUN apt-get update \
    && apt-get install -y gcc \
    libpq-dev \
    python3-dev \
    musl-dev \
    libmagic1 \
    libmagic-dev \
    && rm -rf /var/lib/apt/lists/*


RUN apt-get update && apt-get install -y \
    libreoffice

## copy Python dependencies from build image
# COPY --from=build /opt/venv /opt/venv

# install dependencies
RUN pip install --upgrade pip
COPY requirements.txt .
RUN pip install -r requirements.txt

## set working directory
WORKDIR /app

## set environment variables
ENV PYTHONDONTWRITEBYTECODE 1
ENV PYTHONUNBUFFERED 1
ENV PATH="/opt/venv/bin:$PATH"
# copy project
COPY . /app

EXPOSE 8000

#recommend (2 x $num_cores) + 1 as the number of workers to start
# CMD ["python", "run_daphne.py"]
CMD ["uvicorn", "cls_backend.asgi:application", "--host", "0.0.0.0", "--port", "8000"]

