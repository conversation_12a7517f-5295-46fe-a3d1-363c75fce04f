from django.db import models
from django.contrib.auth import get_user_model
from django.utils import timezone
import json

User = get_user_model()

class RequestLog(models.Model):
    timestamp = models.DateTimeField(default=timezone.now)
    user = models.ForeignKey(User, on_delete=models.SET_NULL, null=True, blank=True)
    feature_name = models.CharField(max_length=255)
    method = models.CharField(max_length=10)
    status_code = models.PositiveSmallIntegerField()
    response = models.TextField(blank=True, null=True)  # Store response content
    parameters = models.JSONField(blank=True, null=True)  # Store query/POST params
    meta = models.JSONField(blank=True, null=True)       # Store headers/META

    def __str__(self):
        return f"{self.method} {self.feature_name} ({self.status_code})"