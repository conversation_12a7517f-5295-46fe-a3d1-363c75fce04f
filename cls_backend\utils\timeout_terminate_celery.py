from celery import Task
from celery.exceptions import SoftTimeLimitExceeded
from cls_backend.constants import STATUS_FAILED
from cls_backend.models import Document
import logging

logger = logging.getLogger("cls")

class BaseTask(Task):
    autoretry_for = (Exception,)
    retry_kwargs = {'max_retries': 3, 'countdown': 5}
    retry_backoff = True
    countdown = 5

    def on_failure(self, exc, task_id, args, kwargs, einfo):
        """
        Ghi log khi task thất bại
        """
        document_id = args[0] # TODO: error in case of task failure
        if isinstance(exc, SoftTimeLimitExceeded):
            logger.error(f"SoftTimeLimitExceeded: Task {task_id} for document {document_id} exceeded the soft time limit.")
        elif isinstance(exc, Exception):
            logger.error(f"Task {task_id} failed: {exc}")

        # Cập nhật trạng thái document nếu gặp lỗi
        try:
            document = Document.objects.get(id=document_id)
            document.status = STATUS_FAILED
            document.save(update_fields=['status'])
            logger.error(f"Document {document_id} cập nhật trạng thái FAILED do lỗi.")
        except Document.DoesNotExist:
            logger.error(f"Document {document_id} không tồn tại hoặc đã bị xóa.")
        except Exception as e:
            logger.exception(f"Lỗi khi cập nhật trạng thái FAILED cho Document {document_id}: {e}")

        super().on_failure(exc, task_id, args, kwargs, einfo)

    def on_timeout(self, task_id, args, kwargs):
        """
        Hàm này sẽ chạy khi task bị timeout.
        """
        document_id = args[0]
        logger.error(f"Task {task_id} for document {document_id} exceeded the time limit and is being terminated.")
        
        # Cập nhật trạng thái document do timeout
        try:
            document = Document.objects.get(id=document_id)
            document.status = STATUS_FAILED
            document.save(update_fields=['status'])
            logger.error(f"Document {document_id} cập nhật trạng thái FAILED do timeout.")
        except Document.DoesNotExist:
            logger.error(f"Document {document_id} không tồn tại hoặc đã bị xóa.")
        except Exception as e:
            logger.exception(f"Lỗi khi cập nhật trạng thái FAILED cho Document {document_id}: {e}")
