before_script:
  - docker login -u $REGISTRY_IMAGE_USER -p $REGISTRY_IMAGE_TOKEN $CI_REGISTRY

workflow:
  rules:
    - if: $CI_COMMIT_BRANCH == "main"
      variables:
        REGISTRY_IMAGE: registry.gitlab.com/ailabcati/prod/cls-web-backend
    - if: $CI_COMMIT_BRANCH == "develop"
      variables:
        REGISTRY_IMAGE: registry.gitlab.com/ailabcati/dev/cls-web-backend
    - if: $CI_COMMIT_BRANCH == "develop_v3"
      variables:
        REGISTRY_IMAGE: registry.gitlab.com/ailabcati/dev/cls-web-backend-v3

build:
  stage: build
  script:
    - docker pull $REGISTRY_IMAGE:latest || true
    - docker build --cache-from $REGISTRY_IMAGE:latest --tag $REGISTRY_IMAGE:$CI_COMMIT_SHA --tag $REGISTRY_IMAGE:latest .
    - docker push $REGISTRY_IMAGE:$CI_COMMIT_SHA
    - docker push $REGISTRY_IMAGE:latest
  only:
    - main
    - develop
    - develop_v3

deploy:
  stage: deploy
  # image: alpine:3.11
  image: cmcatilab/alpine:3.11_git_kustomize
  before_script:
    # - apk add --no-cache git curl bash
    # - curl -s "https://raw.githubusercontent.com/kubernetes-sigs/kustomize/master/hack/install_kustomize.sh"  | bash
    # - mv kustomize /usr/local/bin/
    - git remote set-url origin https://$ARGOCD_REGISTRY_USER:$ARGOCD_REGISTRY_TOKEN@$ARGOCD_REPO
    - git clone https://$ARGOCD_REGISTRY_USER:$ARGOCD_REGISTRY_TOKEN@$ARGOCD_REPO
    - cd argo-config
  script:
    - git checkout -B main
    - cd $CONFIG_DIR
    - kustomize edit set image $REGISTRY_IMAGE:$CI_COMMIT_SHA
    - git config --global user.email "<EMAIL>"
    - git config --global user.name "Gitlab Runner"
    - git commit -am "CLS BE - $CI_COMMIT_BRANCH - $CI_COMMIT_MESSAGE"
    - git push origin main
  only:
    - main
    - develop
    - develop_v3