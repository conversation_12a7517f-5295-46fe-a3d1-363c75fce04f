version: '3.1'

x-be-env: &be-env
  POSTGRES_USER: postgres
  POSTGRES_PASSWORD: LibgSsr4399K
  POSTGRES_DB: cls_btp
  POSTGRES_PORT: 5432
  POSTGRES_HOST: postgres

  OCR_HOST: http://********:9816
  EXTRACT_OCR: /ocr/upload_ocr_large_file
  OCR_CHECK_STATUS: /check_process_status
  CLS_HOST: http://********:6970
  LEGAL_SEARCH_BY_QUERY: /v2/legal_search
  SEARCH_EFFECTIVE_TIME: /conflict/thoi_gian_hieu_luc/search
  CHECK_EFFECTIVE_TIME: /conflict/thoi_gian_hieu_luc/check xsw2
  THAM_QUYEN_BAN_HANH: /conflict/tham_quyen_ban_hanh
  THAM_QUYEN_HINH_THUC: /conflict/tham_quyen_hinh_thuc
  KEYWORDS_LOAI_VAN_BAN: /utils/keywords/loai_van_ban
  KEYWORDS_CO_QUAN_BAN_HANH: /utils/keywords/co_quan_ban_hanh
  KEYWORDS_CHU_DE_PHAP_DIEN: /utils/keywords/domains
  KEYWORDS_SUA_DOI_BO_SUNG: /utils/keywords/sua_doi_bo_sung
  KEYWORDS_TRANG_THAI_HIEU_LUC: /utils/keywords/trang_thai_hieu_luc,nc

  AWS_S3_ENDPOINT_URL: https://s3.cmcati.vn
  AWS_ACCESS_KEY_ID: YF89NNJ3MQTE47WXENGY
  AWS_SECRET_ACCESS_KEY: jMdHSx9fMEEXCHCNEwUV0n9FE7BG1TTeBv9RNMhW
  AWS_STORAGE_BUCKET_NAME: cls

  REDIS_HOST: ********
  REDIS_PORT: 6374
  REDIS_PASSWORD: 6pkbXlDKA160
  # REDIS_TTL: 86400
  CELERY_REDIS_DB: 1
  CHANNEL_REDIS_DB: 7

  OCR_REDIS_HOST: *********
  OCR_REDIS_PORT: 6379
  OCR_REDIS_PASSWORD: Hk3HkkaU3GlQ
  OCR_REDIS_DB: 0
  OCR_REDIS_TTL: 86400

  RBMQ_HOST: rabbitmq
  RBMQ_PORT: 5672
  RBMQ_USER_NAME: root
  RBMQ_PASSWORD: LibgSsr4399K

  LOG_LEVEL: 10
  DOC_TO_DOCX: http://********9:30510/convert

  CONFLICT_TIMEOUT: 300

  ELK_USERNAME: elastic
  ELK_PASSWORD: dTNUt33Oxg52
  ELK_ENDPOINT: http://*********:9200
  ELK_DOCUMENT_INDEX: law_documents_t3

  LLM_API_KEY: e34321d1dcsd
  LLM_HOST: df3141fds1
  MODEL: 13212đsag
  REWRITE_MODEL: dgdfgfdytw

  IE_VBHC_URL: http://********:9816/ie/document_ie
  ES_SEARCH_DIEU_KHOAN: /law_terms/_search

services:
  # web:
  #   build: .
  #   image: cls-backend:local
  #   container_name: django_app
  #   command: sh -c "python run_daphne.py"
  #   volumes:
  #     - ./logs:/app/logs
  #   environment:
  #     <<: [*be-env]
  #   ports:
  #     - "8000:8000"
  #   networks:
  #     - web-net

  # celery_worker:
  #   image: cls-backend:local
  #   container_name: celery_worker_default
  #   command: sh -c "celery -A cls_backend worker --loglevel info -P gevent -Q default"
  #   environment:
  #     <<: [*be-env]
  #   volumes:
  #     - ./logs:/app/logs
  #   networks:
  #     - web-net

  # # celery_ocr:
  # #   image: cls-backend:local
  # #   container_name: celery_worker_ocr
  # #   command: sh -c "celery -A cls_backend worker --loglevel info -P gevent -Q ocr"
  # #   environment:
  # #     <<: [*be-env]
  # #   volumes:
  # #     - ./logs:/app/logs
  # #   networks:
  # #     - web-net

  postgres:
    image: postgres:14.2
    restart: always
    environment:
      POSTGRES_USER: ${POSTGRES_USER}
      POSTGRES_PASSWORD: ${POSTGRES_PASSWORD}
      POSTGRES_DB: ${POSTGRES_DB}
      PGDATA: /data/postgres
    ports:
      - "5432:5432"
    volumes:
      - postgres-data:/data/postgres
    networks:
      - web-net

  flower:
    image: mher/flower:2.0
    restart: always
    environment:
      CELERY_BROKER_URL: amqp://${RBMQ_USER_NAME}:${RBMQ_PASSWORD}@rabbitmq:5672/
      CELERY_BACKEND_URL: redis://default:${REDIS_PASSWORD}@${REDIS_HOST}:${REDIS_PORT}/${CELERY_REDIS_DB}
      FLOWER_PORT: 8090
    ports:
      - 8090:8090
    depends_on:
      - rabbitmq
    networks:
      - web-net

  rabbitmq:
    image: rabbitmq:3.13-management-alpine
    environment:
      RABBITMQ_DEFAULT_USER: ${RBMQ_USER_NAME}
      RABBITMQ_DEFAULT_PASS: ${RBMQ_PASSWORD}
    ports:
      - 5672:5672
      - 15672:15672
    volumes:
      - rabbitmq-data:/var/lib/rabbitmq/
    networks:
      - web-net

volumes:
  postgres-data:
  redis-data:
  rabbitmq-data:

networks:
  web-net:
    driver: bridge
  