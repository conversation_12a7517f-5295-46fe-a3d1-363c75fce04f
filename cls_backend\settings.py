"""
Django settings for cls_backend project.

Generated by 'django-admin startproject' using Django 5.0.1.

For more information on this file, see
https://docs.djangoproject.com/en/5.0/topics/settings/

For the full list of settings and their values, see
https://docs.djangoproject.com/en/5.0/ref/settings/
"""
from datetime import timedelta
import os
import logging

os.environ["DJANGO_ALLOW_ASYNC_UNSAFE"] = "true"

from pathlib import Path

from decouple import config

# Build paths inside the project like this: BASE_DIR / 'subdir'.
BASE_DIR = Path(__file__).resolve().parent.parent

# Quick-start development settings - unsuitable for production
# See https://docs.djangoproject.com/en/5.0/howto/deployment/checklist/

# SECURITY WARNING: keep the secret key used in production secret!
SECRET_KEY = 'django-insecure-cm+b0h$14xo^onqi1o9p0me_j0drvw+9$=1)%(#ojk_&qai&t)'
DOWNLOAD_SECRET_KEY = 'django-insecure-download+pu3clt5-okllgf^#=l*sq_y5-&a-i9@me!_5)(b@1-z5m&19d'

# SECURITY WARNING: don't run with debug turned on in production!
DEBUG = True

CORS_ALLOW_CREDENTIALS = True
# CORS_ALLOW_ALL_ORIGINS = True
# FRONTEND_URL = config('FRONTEND_URL')
# BACKEND_URL = config('BACKEND_URL')
allowed_domain = [
    "https://cls.cmcati.vn",
    "https://dev-cls.cmcati.vn",
    "https://api-cls.cmcati.vn",
    "https://dev-api-cls.cmcati.vn",
    "https://clsbtp.cmcati.vn",
    "https://devui-cls.cmcati.vn",
    "https://api-devui-cls.cmcati.vn",
    "https://clsqh.cmcati.vn",
]

if DEBUG:
    allowed_domain.append("http://localhost:4200")
    allowed_domain.append("http://127.0.0.1:4200")

CSRF_TRUSTED_ORIGINS = CORS_ALLOWED_ORIGINS = allowed_domain
ALLOWED_HOSTS = ["*"]
# Application definition

INSTALLED_APPS = [
    'django.contrib.admin',
    'django.contrib.auth',
    # Application
    'cls_backend',
    'chatbot',
    'organization',
    'request_log',
    'dashboard',
    'maintainence',

    'django.contrib.contenttypes',
    'django.contrib.sessions',
    'django.contrib.messages',
    'django.contrib.staticfiles',
    'auditlog',
    # cors

    'corsheaders',
    "rest_framework",
    "rest_framework_simplejwt",
    'rest_framework_simplejwt.token_blacklist',

    # Socket Django channel
    'channels',

    # Django storage
    'storages',
    'celery'
]

MIDDLEWARE = [
    'django.middleware.security.SecurityMiddleware',
    'django.contrib.sessions.middleware.SessionMiddleware',
    'corsheaders.middleware.CorsMiddleware',
    'cls_backend.middlewares.UnauthorizedResponseRemovalMiddleware',
    'django.middleware.common.CommonMiddleware',
    'django.middleware.locale.LocaleMiddleware',
    'django.middleware.csrf.CsrfViewMiddleware',
    'django.contrib.auth.middleware.AuthenticationMiddleware',
    'django.contrib.messages.middleware.MessageMiddleware',
    'django.middleware.clickjacking.XFrameOptionsMiddleware',
    'whitenoise.middleware.WhiteNoiseMiddleware',
    'maintainence.middlewares.MaintenanceMiddleware',
]

ROOT_URLCONF = 'cls_backend.urls'
# CORS_ALLOW_ALL_ORIGINS = True
CORS_ALLOW_CREDENTIALS = True
CORS_ORIGIN_ALLOW_ALL = True
CORS_ALLOW_HEADERS = [
    "accept",
    "accept-encoding",
    "authorization",
    "content-type",
    "dnt",
    "origin",
    "user-agent",
    "x-csrftoken",
    "x-requested-with",
    'enctype',
]

TEMPLATES = [
    {
        'BACKEND': 'django.template.backends.django.DjangoTemplates',
        'DIRS': [],
        'APP_DIRS': True,
        'OPTIONS': {
            'context_processors': [
                'django.template.context_processors.debug',
                'django.template.context_processors.request',
                'django.contrib.auth.context_processors.auth',
                'django.contrib.messages.context_processors.messages',
            ],
        },
    },
]

WSGI_APPLICATION = 'cls_backend.wsgi.application'
ASGI_APPLICATION = 'cls_backend.routing.application'

# Database
# https://docs.djangoproject.com/en/5.0/ref/settings/#databases

DATABASES = {
    'default': {
        'ENGINE': 'django.db.backends.postgresql',
        'NAME': config('POSTGRES_DB'),
        'USER': config('POSTGRES_USER'),
        'PASSWORD': config('POSTGRES_PASSWORD'),
        'HOST': config('POSTGRES_HOST', default='localhost'),
        'PORT': config('POSTGRES_PORT', default=5432),
    }
}


AUDITLOG_TWO_STEP_MIGRATION = True
AUDITLOG_CID_GETTER = None
AUDITLOG_INCLUDE_ALL_MODELS = True
# Password validation
# https://docs.djangoproject.com/en/5.0/ref/settings/#auth-password-validators

AUTH_PASSWORD_VALIDATORS = [
    {
        'NAME': 'django.contrib.auth.password_validation.UserAttributeSimilarityValidator',
    },
    {
        'NAME': 'django.contrib.auth.password_validation.MinimumLengthValidator',
    },
    {
        'NAME': 'django.contrib.auth.password_validation.CommonPasswordValidator',
    },
    {
        'NAME': 'django.contrib.auth.password_validation.NumericPasswordValidator',
    },
]

PASSWORD_HASHERS = [
    'django.contrib.auth.hashers.PBKDF2PasswordHasher',
    'django.contrib.auth.hashers.PBKDF2SHA1PasswordHasher',
    'django.contrib.auth.hashers.Argon2PasswordHasher',
    'django.contrib.auth.hashers.BCryptSHA256PasswordHasher',
]

REST_FRAMEWORK = {
    'DEFAULT_RENDERER_CLASSES': [
        'rest_framework.renderers.JSONRenderer',
    ],
    'DEFAULT_PERMISSION_CLASSES': [
        'rest_framework.permissions.DjangoModelPermissionsOrAnonReadOnly'
    ],
    'DEFAULT_AUTHENTICATION_CLASSES': [
        "rest_framework.authentication.BasicAuthentication",
        "rest_framework_simplejwt.authentication.JWTAuthentication",
        # 'cls_backend.modules.auth.backends.JWTAuthentication',
    ],
    'DEFAULT_FILTER_BACKENDS': ['django_filters.rest_framework.DjangoFilterBackend'],
    'DEFAULT_PAGINATION_CLASS': 'cls_backend.pagination.CustomPageNumberPagination',
    'EXCEPTION_HANDLER': 'cls_backend.utils.exception_handler.custom_exception_handler',
    'DEFAULT_THROTTLE_CLASSES': [
        'rest_framework.throttling.UserRateThrottle',
    ]
}

REST_AUTH_SERIALIZERS = {
    'USER_DETAILS_SERIALIZER': 'cls_backend.modules.auth.serializers.UserSerializer',
}

"""
MEDIA PATH
"""
MEDIA_ROOT = os.path.join(BASE_DIR, 'media')
MEDIA_URL = 'media/'
PRIVATE_MEDIA_ROOT = os.path.join(BASE_DIR, 'private_media')

# Internationalization
# https://docs.djangoproject.com/en/5.0/topics/i18n/

LANGUAGE_CODE = 'en-us'

TIME_ZONE = 'Asia/Ho_Chi_Minh'

USE_I18N = True

USE_TZ = True

# Static files (CSS, JavaScript, Images)
# https://docs.djangoproject.com/en/5.0/howto/static-files/

STATIC_URL = 'static/'

# Default primary key field type
# https://docs.djangoproject.com/en/5.0/ref/settings/#default-auto-field

DEFAULT_AUTO_FIELD = 'django.db.models.BigAutoField'

AUTH_USER_MODEL = 'cls_backend.User'

# config django channels
REDIS_HOST = config('REDIS_HOST', default='localhost')
REDIS_PORT = config('REDIS_PORT', default='6379')
REDIS_PASSWORD = config('REDIS_PASSWORD', default=None)

CHANNEL_REDIS_DB = config('CHANNEL_REDIS_DB', default='0')
CHANNEL_REDIS_URL = f"redis://{REDIS_HOST}:{REDIS_PORT}/{CHANNEL_REDIS_DB}"
if REDIS_PASSWORD:
    CHANNEL_REDIS_URL = f"redis://:{REDIS_PASSWORD}@{REDIS_HOST}:{REDIS_PORT}/{CHANNEL_REDIS_DB}"
CHANNEL_LAYERS = {
    "default": {
        "BACKEND": "channels_redis.pubsub.RedisPubSubChannelLayer",
        "CONFIG": {
            "hosts": [(CHANNEL_REDIS_URL)],
        },
    },
}

# rabbitmq settings
RBMQ_HOST = config('RBMQ_HOST')
RBMQ_PORT = int(config('RBMQ_PORT'))
RBMQ_USER_NAME = config('RBMQ_USER_NAME')
RBMQ_PASSWORD = config('RBMQ_PASSWORD')
VHOST = config('VHOST', default='/')
"""
Celery
"""
# Celery configuration

CELERY_REDIS_DB = config('CELERY_REDIS_DB', default='3')
CELERY_REDIS_URL = f"redis://{REDIS_HOST}:{REDIS_PORT}/{CELERY_REDIS_DB}"
if REDIS_PASSWORD:
    CELERY_REDIS_URL = f"redis://:{REDIS_PASSWORD}@{REDIS_HOST}:{REDIS_PORT}/{CELERY_REDIS_DB}"

CELERY_BROKER_URL = CELERY_REDIS_URL#f"amqp://{RBMQ_USER_NAME}:{RBMQ_PASSWORD}@{RBMQ_HOST}:{RBMQ_PORT}/{VHOST}"
# CELERY_BROKER_URL = f"amqp://{RBMQ_USER_NAME}:{RBMQ_PASSWORD}@{RBMQ_HOST}:{RBMQ_PORT}/{VHOST}"
CELERY_RESULT_BACKEND = CELERY_REDIS_URL  # URL của Redis
CELERY_ACCEPT_CONTENT = ['application/json']
CELERY_TASK_SERIALIZER = 'json'
CELERY_RESULT_SERIALIZER = 'json'
CELERY_TIMEZONE = TIME_ZONE

CELERY_BEAT_SCHEDULE = {}
CELERY_TASK_ACKS_LATE = True
CELERY_TASK_REJECT_ON_WORKER_LOST = True
CELERY_TASK_TRACK_STARTED = True
# django-storages settings
DEFAULT_FILE_STORAGE = "storages.backends.s3boto3.S3Boto3Storage"

AWS_ACCESS_KEY_ID = config('AWS_ACCESS_KEY_ID', default='minioadmin')
AWS_SECRET_ACCESS_KEY = config('AWS_SECRET_ACCESS_KEY', default='minioadmin')
AWS_STORAGE_BUCKET_NAME = config('AWS_STORAGE_BUCKET_NAME', default='cls-doc')
AWS_STORAGE_BUCKET_NAME_DOC = config('AWS_STORAGE_BUCKET_NAME_DOC', default='cls-doc')
AWS_S3_ENDPOINT_URL = config('AWS_S3_ENDPOINT_URL', default='http://minio:9000')
AWS_S3_REGION_NAME = config('AWS_S3_REGION_NAME', default='')
AWS_DEFAULT_ACL = None
AWS_QUERYSTRING_AUTH = True
AWS_S3_FILE_OVERWRITE = False
# django redis
CACHE_REDIS_DB = config('CACHE_REDIS_DB', default=CHANNEL_REDIS_DB)
CACHES = {
    "default": {
        "BACKEND": "django_redis.cache.RedisCache",
        "LOCATION": f"redis://:{REDIS_PASSWORD}@{REDIS_HOST}:{REDIS_PORT}/{CACHE_REDIS_DB}",
        "TIMEOUT": 24 * 60 * 60,
        "OPTIONS": {
            "CLIENT_CLASS": "django_redis.client.DefaultClient",
            'MAX_ENTRIES': 1000
        }
    }
}
OCR_HOST = config('OCR_HOST', default='http://********:9815')

DATA_UPLOAD_MAX_MEMORY_SIZE = 262144000

os.makedirs('logs', exist_ok=True)
log_level = config("LOG_LEVEL", cast=int, default=logging.DEBUG)
LOGGING = {
    "version": 1,
    "disable_existing_loggers": False,
    "formatters": {
        "simple": {
            "format": "[%(asctime)s] %(levelname)-2s [%(name)s] [%(filename)s:%(lineno)d] %(message)s",
            "datefmt": "%Y-%m-%d %H:%M:%S",
        },
    },
    "handlers": {
        "applogfile": {
            "level": log_level,
            "class": "logging.handlers.RotatingFileHandler",
            "filename": os.path.join(BASE_DIR, "logs", "django.log"),
            "backupCount": 10,
            "formatter": "simple",
        },
        "console": {
            "level": log_level,
            "class": "logging.StreamHandler",
            "formatter": "simple",
        },
        "debug_log": {
            "level": log_level,
            "class": "logging.handlers.RotatingFileHandler",
            "filename": os.path.join(BASE_DIR, "logs", "api.log"),
            "backupCount": 10,
            "formatter": "simple",
            'encoding': 'UTF-8'
        },
    },
    "loggers": {
        "django": {
            "handlers": ["applogfile", "console"],
            "level": os.getenv("DJANGO_LOG_LEVEL", "INFO"),
        },
        "cls": {
            "handlers": ["debug_log"],
            "level": os.getenv("DJANGO_LOG_LEVEL", log_level),
        },
        "celery": {
            "handlers": ["debug_log"],
            "level": log_level,
        },
    },
}

OCR_REDIS_HOST = config('OCR_REDIS_HOST', default='*********')
OCR_REDIS_PORT = config('OCR_REDIS_PORT', default='6379')
OCR_REDIS_PASSWORD = config('OCR_REDIS_PASSWORD', default=None)
OCR_REDIS_TTL = config('OCR_REDIS_TTL', default=None)
OCR_REDIS_DB = int(config('OCR_REDIS_DB', default=0))


EMAIL_BACKEND = 'django.core.mail.backends.smtp.EmailBackend'
EMAIL_HOST = config('EMAIL_HOST', default='smtp.gmail.com')
EMAIL_PORT = config('EMAIL_PORT', default=587)
EMAIL_USE_TLS = config('EMAIL_USE_TLS', default = True)
EMAIL_HOST_USER = config('EMAIL_HOST_USER', default = '<EMAIL>')
EMAIL_HOST_PASSWORD = config('EMAIL_HOST_PASSWORD',default= 'equp lbrg snts aiqs')
DEFAULT_FROM_EMAIL = config('DEFAULT_FROM_EMAIL', default = '<EMAIL>')

SIMPLE_JWT = {
    "ACCESS_TOKEN_LIFETIME": timedelta(hours=8),
    "REFRESH_TOKEN_LIFETIME": timedelta(days=30),
    "ROTATE_REFRESH_TOKENS":False,  
    "BLACKLIST_AFTER_ROTATION":False,  
    "UPDATE_LAST_LOGIN": True,
    "ALGORITHM": "HS256",
    "SIGNING_KEY": SECRET_KEY,
    "VERIFYING_KEY": None,
    "AUDIENCE": None,
    "ISSUER": None,
    "JWK_URL": None,
    "LEEWAY": 0,
    "AUTH_HEADER_TYPES": ("Bearer",),
    "AUTH_HEADER_NAME": "HTTP_AUTHORIZATION",
    "USER_ID_FIELD": "id",
    "USER_ID_CLAIM": "user_id",
    "USER_AUTHENTICATION_RULE": "rest_framework_simplejwt.authentication.default_user_authentication_rule",
    "AUTH_TOKEN_CLASSES": ("rest_framework_simplejwt.tokens.AccessToken",),
    "TOKEN_TYPE_CLAIM": "token_type",
    "JTI_CLAIM": "jti",
    "SLIDING_TOKEN_REFRESH_EXP_CLAIM": "refresh_exp",
    "SLIDING_TOKEN_LIFETIME": timedelta(minutes=5),
    "SLIDING_TOKEN_REFRESH_LIFETIME": timedelta(days=1),
}

AUDITLOG_DISABLE_ON_RAW_SAVE = True

PASSWORD_RESET_TIMEOUT = 28800