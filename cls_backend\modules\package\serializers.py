from cls_backend.models import Package, Quota
from cls_backend.modules.auth.serializers import UserSerializer
from rest_framework import serializers

class PackageSerializer(serializers.ModelSerializer):
    upload_type = serializers.CharField(required=False, allow_blank=True)
    download_type = serializers.CharField(required=False, allow_blank=True)
    class Meta:
        model = Package
        fields = '__all__'  

class QuotaSerializer(serializers.ModelSerializer):
    user = UserSerializer(read_only=True)  
    package = PackageSerializer(read_only=True)  
    package_id = serializers.PrimaryKeyRelatedField(
        queryset=Package.objects.all(), source='package', write_only=True
    )

    class Meta:
        model = Quota
        fields = ['user', 'package', 'package_id', 'created_time', 'last_updated']