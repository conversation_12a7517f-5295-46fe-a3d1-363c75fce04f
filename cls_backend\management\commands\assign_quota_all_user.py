from django.core.management.base import BaseCommand
from cls_backend.models import User, Quota, Package

class Command(BaseCommand):
    help = "Tạo quota cho tất cả các tài khoản chưa có"

    def handle(self, *args, **kwargs):
        default_package = Package.objects.first()  # <PERSON>ọn gói mặc định
        if not default_package:
            self.stdout.write(self.style.ERROR("Không tìm thấy gói Package nào!"))
            return

        users_without_quota = User.objects.filter(quota__isnull=True)
        if not users_without_quota.exists():
            self.stdout.write(self.style.SUCCESS("Tất cả người dùng đều đã có quota!"))
            return

        created_count = 0
        for user in users_without_quota:
            Quota.objects.create(user=user, package=default_package)
            created_count += 1

        self.stdout.write(self.style.SUCCESS(f"Đã tạo {created_count} quota mới!"))
