# __CLS__ Web Backend



## Getting started

## Installation

### 1. Install require package 
```commandline
pip install -r requirements.txt
```

### 2. Run database
```commandline
docker-compose up -d
```
### 3. Data Migration
```commandline
python manage.py makemigrations cls_backend
python manage.py migrate
```
### 4. Create super admin account
```commandline
python manage.py createsuperuser
```
### 5. Run Service
```commandline
python run_daphne.py
```
### 5. Run Celery
##### Window: Windows does not support celery 4.0+, you may have log: INFO/SpawnPoolWorker-6 child process 16876 calling self.run().

```commandline
pip install gevent
celery -A cls_backend worker --loglevel=info -P gevent
```

##### Other os: ubuntu, macos: run normally

```commandline
celery -A voice worker --loglevel=info
```
