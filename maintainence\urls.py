from django.urls import include, re_path, path
from rest_framework import routers
from .views import MaintenancePlanViewSet

# Routers provide a way of automatically determining the URL conf.
router = routers.DefaultRouter()
# router.register("chat", LLMAPIView)
# Wire up our API using automatic URL routing.
# Additionally, we include login URLs for the browsable API.
router.register('plan', MaintenancePlanViewSet, basename='plan') 
urlpatterns = [
    path('', include(router.urls)),
]