# Generated by Django 4.2 on 2025-06-30 08:46

from django.conf import settings
from django.db import migrations, models
import django.db.models.deletion
import django.utils.timezone


class Migration(migrations.Migration):

    initial = True

    dependencies = [
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
    ]

    operations = [
        migrations.CreateModel(
            name="RequestLog",
            fields=[
                (
                    "id",
                    models.BigAutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                ("timestamp", models.DateTimeField(default=django.utils.timezone.now)),
                ("feature_name", models.CharField(max_length=255)),
                ("method", models.CharField(max_length=10)),
                ("status_code", models.PositiveSmallIntegerField()),
                ("response", models.TextField(blank=True, null=True)),
                ("parameters", models.J<PERSON><PERSON>ield(blank=True, null=True)),
                ("meta", models.<PERSON><PERSON><PERSON>ield(blank=True, null=True)),
                (
                    "user",
                    models.ForeignKey(
                        blank=True,
                        null=True,
                        on_delete=django.db.models.deletion.SET_NULL,
                        to=settings.AUTH_USER_MODEL,
                    ),
                ),
            ],
        ),
    ]
