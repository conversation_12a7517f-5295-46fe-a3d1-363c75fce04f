from rest_framework.routers import Default<PERSON>outer
from django.urls import include, path
from .views import *

urlpatterns = [
    # user
    path('user/summary', ActivityUserSummaryView.as_view()),
    path('user/by-day', ActivityUserByDayView.as_view()),
    path("user/summary/ocr/", OCRSummaryViewUser.as_view(), name="ocr-summary"),
    path("user/summary/document/", DocumentSummaryViewUser.as_view(), name="document-summary"),
    path("user/summary/legal_search/", LegalSearchSummaryViewUser.as_view(), name="legal-search-summary"),
    # admin
    path('admin/user-summary', ActivityAdminUserSummaryView.as_view()),
    path('admin/summary', ActivityAdminSummaryView.as_view()),
    path('admin/by-day', ActivityAdminByDayView.as_view()),
]
