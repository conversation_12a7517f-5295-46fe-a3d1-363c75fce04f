from rest_framework import serializers
from cls_backend.models import Report

class ReportSerializer(serializers.ModelSerializer):
    user = serializers.PrimaryKeyRelatedField(read_only=True)
    
    class Meta:
        model = Report
        fields = ['id', 'user', 'content', 'report_type', 'status', 'attachments', 
                 'created_at', 'updated_at']
        read_only_fields = ['user', 'created_at', 'updated_at']
    
    def create(self, validated_data):
        # Set the current user as the report creator
        validated_data['user'] = self.context['request'].user
        return super().create(validated_data) 