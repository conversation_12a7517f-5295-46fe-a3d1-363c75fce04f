import os
import subprocess
from datetime import datetime
import requests
from celery import shared_task
from decouple import config
from cls_backend.constants import STATUS_CONFLICT, STATUS_FAILED, STATUS_IE, STATUS_SUCCESS
from cls_backend.models import Document
import logging
import tempfile
import platform
from docx2pdf import convert as docx2pdf_convert
from cls_backend.modules.notification.event import EVENT_CONFLICT_DOING, EVENT_IE_DOING, EVENT_TQND_DONE
from cls_backend.modules.notification.utils import send_notifications
from cls_backend.utils.timeout_terminate_celery import BaseTask
from celery.exceptions import SoftTimeLimitExceeded

import fitz

logger = logging.getLogger("cls")

SOFT_TASK_TIMEOUT = int(config('SOFT_TASK_TIMEOUT', default=300))
HARD_TASK_TIMEOUT = int(config('HARD_TASK_TIMEOUT', default=330))

def convert_docx_to_pdf_cross_platform(input_path: str, output_path: str):
    """
    Chuyển file DOCX sang PDF hỗ trợ cả Windows (docx2pdf) và Linux (LibreOffice).
    """
    if platform.system() == "Windows":
        docx2pdf_convert(input_path, output_path)
    else:
        output_dir = os.path.dirname(output_path)
        try:
            subprocess.run([
                "soffice", "--headless",
                "--convert-to", "pdf",
                "--outdir", output_dir,
                input_path
            ], check=True)
        except subprocess.CalledProcessError as e:
            raise RuntimeError(f"LibreOffice conversion failed: {e}")

        if not os.path.exists(output_path):
            raise FileNotFoundError(f"Converted PDF not found at: {output_path}")

# @shared_task(bind=True, base=BaseTask, time_limit=HARD_TASK_TIMEOUT, soft_time_limit=SOFT_TASK_TIMEOUT)
@shared_task(bind=True, base=BaseTask)
def handle_ie(self, document_id):
    """
    Task xử lý import/export tài liệu và cập nhật thông tin từ IE.
    Chỉ gửi trang đầu tiên của tài liệu lên API.
    """
    try:
        document = Document.objects.get(id=document_id)
        document.status = STATUS_IE
        if document.compare_status == STATUS_SUCCESS and document.authority_status == STATUS_SUCCESS:
            document.percentage = 75
        else:
            document.percentage = 50
        document.save(update_fields=['status','percentage'])
        send_notifications(
            user_id=document.created_by_id,
            event=EVENT_IE_DOING,
            data={"name": document.name, "status": STATUS_IE,"document_id":str(document.id)},
            percentage=75 if document.compare_status == STATUS_SUCCESS and document.authority_status == STATUS_SUCCESS else 50
        )
        if not document.origin or not document.origin.storage.exists(document.origin.name):
            logger.debug(f"Document has no origin file or file is missing.{document_id}")
            raise ValueError("Document has no origin file or file is missing.")

        # Xử lý file tạm
        suffix = os.path.splitext(document.origin.name)[1]
        with tempfile.NamedTemporaryFile(suffix=suffix, delete=False) as temp_origin_file:
            temp_origin_file.write(document.origin.read())
            temp_origin_file.flush()
            origin_path = temp_origin_file.name

        # Chuyển đổi và trích xuất trang đầu tiên
        if suffix.lower() == ".pdf":
            first_page_path = extract_first_page_from_pdf(origin_path)
        elif suffix.lower() == ".docx":
            pdf_temp_path = origin_path.replace(".docx", ".pdf")
            convert_docx_to_pdf_cross_platform(origin_path, pdf_temp_path)
            first_page_path = extract_first_page_from_pdf(pdf_temp_path)
        else:
            raise ValueError(f"Unsupported file format: {suffix}")

        # Gửi request tới API để trích xuất thông tin
        with open(first_page_path, 'rb') as f:
            files = {'file': (os.path.basename(first_page_path), f)}
            data = {
                    'return_type': 'admin',
                    'return_now': '1',
                    'extract_formula': '0',
                    'extract_signature': '0',
                    'extract_checkbox': '0',
                    'extract_figure':'1',
                    'is_full_line':'1',
                    'extract_text_in_figure':'0'
                }
            url = config('IE_VBHC_URL')
            
            response = requests.post(url, files=files, data=data)
            response.raise_for_status()
            data = response.json()

        # Lưu thông tin vào Document
        admin = data.get("admin", {})
        # document.co_quan_ban_hanh = admin.get("SenderCode", [{}])[0].get("content", "")
        raw_sender = admin.get("SenderCode", [{}])[0].get("content", "").strip()
        words = raw_sender.split()
        half = len(words) // 2
        if len(words) % 2 == 0 and words[:half] == words[half:]:
            document.co_quan_ban_hanh = " ".join(words[:half])
        else:
            document.co_quan_ban_hanh = raw_sender
        source_doc_no = (data.get("admin", {}).get("SourceDocNo", [{}])[0].get("content") or "")
        notation = (data.get("admin", {}).get("Notation", [{}])[0].get("content") or "")
        if source_doc_no and notation:
            if source_doc_no.endswith("/") or notation.startswith("/"):
                document.so_hieu = source_doc_no + notation
            else:
                document.so_hieu = source_doc_no + "/" + notation
        else:
            document.so_hieu = source_doc_no or notation
        # document.so_hieu = (
        #     admin.get("SourceDocNo", [{}])[0].get("content", "") + "/" +
        #     admin.get("Notation", [{}])[0].get("content", "")
        # )
        document.ngay_ban_hanh = admin.get("DocDate", [{}])[0].get("content", "")
        document.trich_yeu = admin.get("Brief", [{}])[0].get("content", "")
        document.loai_van_ban = admin.get("DocType", [{}])[0].get("content", "")
        document.nguoi_ky = admin.get("Signer", [{}])[0].get("content", "")
        document.dia_danh = admin.get("Location", [{}])[0].get("content", "")
        document.ie_done = True
        document.save(update_fields=[
            'ie_done', 'so_hieu', 'co_quan_ban_hanh', 'nguoi_ky',
            'loai_van_ban', 'trich_yeu', 'ngay_ban_hanh'
        ])
        doc = Document.objects.get(pk=document_id)
        if doc.compare_status != STATUS_SUCCESS or doc.authority_status != STATUS_SUCCESS :
                document.percentage = 75
                document.save(update_fields=['percentage'])
                # logger.debug("All task is finished")
                send_notifications(
                    user_id=document.created_by_id, 
                    event=EVENT_CONFLICT_DOING, 
                    data={
                        "name": document.name,
                        "status": STATUS_CONFLICT,
                        "document_id":str(document.id)
                    },
                    percentage=75
                )
        if doc.compare_status == STATUS_SUCCESS and doc.authority_status == STATUS_SUCCESS:
            document.status = STATUS_SUCCESS
            document.percentage = 100
            document.save(update_fields=['status','percentage'])
            send_notifications(
                user_id=document.created_by_id,
                event=EVENT_TQND_DONE,
                data={"name": document.name, "status": STATUS_SUCCESS,"document_id":str(document.id)},
                percentage=100
            )

    except SoftTimeLimitExceeded:
        logger.error(f"Task handle_ie timeout for document {document_id}.")
        self.on_timeout(self.request.id, args=(document_id,), kwargs={})
        document.status = STATUS_FAILED
        document.save(update_fields=['status'])
        send_notifications(
            user_id=document.created_by_id,
            event=EVENT_TQND_DONE,
            data={"name": document.name, "status": STATUS_FAILED,"document_id":str(document.id)},
        )
    except Exception as e:
        logger.exception(f"Error in handle_ie for document {document_id}: {str(e)}")
        self.retry(exc=e, countdown=self.countdown)
        document.status = STATUS_FAILED
        document.save(update_fields=['status'])
        send_notifications(
            user_id=document.created_by_id,
            event=EVENT_TQND_DONE,
            data={"name": document.name, "status": STATUS_FAILED,"document_id":str(document.id)}
        )
        raise
    finally:
        # Xóa file tạm
        for path in [origin_path, 'pdf_temp_path', 'first_page_path']:
            if path in locals() and os.path.exists(locals()[path]):
                try:
                    os.remove(locals()[path])
                except Exception as cleanup_error:
                    logger.warning(f"Cleanup failed for {path}: {cleanup_error}")

def extract_first_page_from_pdf(pdf_path):
    temp_output = tempfile.NamedTemporaryFile(suffix=".pdf", delete=False)
    temp_output.close()
    with fitz.open(pdf_path) as doc:
        # first_page = doc.load_page(0)
        pdf_writer = fitz.open()
        pdf_writer.insert_pdf(doc, from_page=0, to_page=0)
        pdf_writer.save(temp_output.name)
    return temp_output.name

