import json

from asgiref.sync import async_to_sync
from channels.generic.websocket import WebsocketConsumer
import logging


logger = logging.getLogger("cls")


class NotificationConsumer(WebsocketConsumer):

    def connect(self):
        # Checking if the User is logged in
        if self.scope["user"].is_anonymous:
            # Reject the connection
            self.close()
            return
        else:
            self.group_name = str(self.scope["user"].pk)
            async_to_sync(self.channel_layer.group_add)(self.group_name, self.channel_name)
            self.accept()

    def disconnect(self, close_code):
        logger.info(f"WebSocket disconnected with code {close_code}")
        if hasattr(self, "group_name"):
            async_to_sync(self.channel_layer.group_discard)(self.group_name, self.channel_name)
        # self.close()

    def notify(self, event):
        # Sample event structure
        # event = {
        #     'data': {
        #         'id': 64, 
        #         'origin': 'https://s3.cmcati.vn/cls/document/2025/02/28/V%C4%83n_b%E1%BA%A3n_1_FBxqO20.pdf?AWSAccessKeyId=YF89NNJ3MQTE47WXENGY&Signature=bd%2F557jlXHe%2FGergC8NW%2B91IN2A%3D&Expires=1740717366', 
        #         'convert': 'https://s3.cmcati.vn/cls/document/2025/02/28/V%C4%83n_b%E1%BA%A3n_1_FBxqO20.docx?AWSAccessKeyId=YF89NNJ3MQTE47WXENGY&Signature=cHWk1jlisj3qoT5G1AlB4d3DrSA%3D&Expires=1740717366', 
        #         'status': 'Thành công', 
        #         'created_at': 
        #         '2025-02-28 10:36:01', 
        #         'name': 'Văn bản 1.pdf', 
        #         'is_convert': True, 
        #         'percentage': 0}, 
        #     'event': 'CDVB'
        # }
        logger.debug(event)
        self.send(text_data=json.dumps(event))
