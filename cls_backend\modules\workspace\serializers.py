from rest_framework import serializers

from cls_backend.models import WorkSpace, User



class WorkSpaceSerializer(serializers.ModelSerializer):
    user_id = serializers.PrimaryKeyRelatedField(
        source="user", queryset=User.objects.all()
    )

    class Meta:
        model = WorkSpace
        fields = ['id', 'name', 'description', 'user_id', 'created_at', 'updated_at', 'is_deleted', 'is_no_folder']
        
