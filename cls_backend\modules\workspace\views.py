from django.db.models import Q
from rest_framework import status
from rest_framework.decorators import action
from rest_framework.generics import RetrieveAP<PERSON><PERSON>iew, CreateAPIView, ListAPIView, DestroyAPIView
from rest_framework.permissions import IsAuthenticated
from rest_framework.response import Response
from rest_framework.viewsets import GenericViewSet
from cls_backend.modules.workspace.serializers import WorkSpaceSerializer
from cls_backend.models import WorkSpace, Document
from django.db import connection
from request_log.decorators import log_requests


class WorkSpaceViewSet(ListAPIView, RetrieveAPIView, CreateAPIView, DestroyAPIView, GenericViewSet):
    permission_classes = [IsAuthenticated]
    serializer_class = WorkSpaceSerializer
    query_set = WorkSpace.objects.filter(is_deleted=False,is_no_folder = False)

    def get_queryset(self):
        user = self.request.user
        search = self.request.GET.get('search')
        page_size = self.request.GET.get("page_size")
        self.pagination_class.page_size = page_size
        query_set = self.query_set
        query_set = query_set.filter(user=user)
        if search:
            query_set = query_set.filter(Q(name__icontains=search) | Q(description__icontains=search))
        return query_set
    
    # @log_requests("list_workspace")
    def list(self, request):
        queryset = self.get_queryset()
        search = request.query_params.get('search', '')
        sort_field = request.query_params.get('sort_field')
        
        if search:
            queryset = queryset.filter(name__icontains=search)

        queryset = queryset.order_by('-created_at')
        if sort_field:
            queryset = queryset.order_by(sort_field)

        queryset = self.paginate_queryset(queryset)
        serializer = WorkSpaceSerializer(queryset, many=True)
        data = serializer.data

        worpspace_ids = ','.join([f"'{ws['id']}'" for ws in data])

        if worpspace_ids:
            user_document_query = (
                'SELECT COUNT(doc.id), MAX(doc.updated_at), doc.workspace_id '
                'FROM cls_backend_document doc '
                'INNER JOIN cls_backend_workspace ws ON '
                'doc.workspace_id = ws.id AND '
                f'ws.user_id = {request.user.id} AND '
                f'ws.id IN ({worpspace_ids}) AND '
                'ws.is_deleted = False '
                'WHERE doc.deleted_at IS NULL '
                'GROUP BY doc.workspace_id'
            )
            
            with connection.cursor() as cursor:
                cursor.execute(user_document_query)
                rows = cursor.fetchall()

            # Mapping workspace_id → (count, latest_updated)
            doc_info_mapping = {
                str(ws_id): {
                    'doc_count': doc_count,
                    'latest_updated': latest_updated.isoformat() if latest_updated else None
                }
                for doc_count, latest_updated, ws_id in rows
            }

            for ws_data in data:
                doc_info = doc_info_mapping.get(str(ws_data['id']), {})
                ws_data['doc_count'] = doc_info.get('doc_count', 0)
                ws_data['latest_updated'] = doc_info.get('latest_updated')

        response = self.get_paginated_response(data)
        return response

    @log_requests("create_workspace")
    def create(self, request, *args, **kwargs):
        data = request.data.copy()
        data['user_id'] = request.user.id
        
        # Mặc định is_no_folder là False trừ khi được chỉ định
        if 'is_no_folder' not in data:
            data['is_no_folder'] = False
            
        serializer = WorkSpaceSerializer(data=data)
        if serializer.is_valid():
            serializer.save()
            return Response(serializer.data, status=status.HTTP_201_CREATED)
        else:
            return Response(serializer.errors, status=status.HTTP_400_BAD_REQUEST)
        
    # @log_requests("update_workspace")
    def update(self, request, pk):
        instance = WorkSpace.objects.get(pk=pk)
        serializer = WorkSpaceSerializer(instance, data=request.data, partial=True)
        if serializer.is_valid():
            serializer.save()
            return Response(serializer.data, status.HTTP_200_OK)
        else:
            return Response(serializer.errors, status=status.HTTP_400_BAD_REQUEST)
        
    # @log_requests("delete_workspace")
    def destroy(self, request, pk):
        workspace = WorkSpace.objects.get(id=pk)
        workspace.is_deleted = True
        workspace.save(update_fields=['is_deleted'])
        return Response(status=status.HTTP_200_OK)
