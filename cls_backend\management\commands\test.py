from django.core.management.base import BaseCommand
from cls_backend.models import User, <PERSON>uo<PERSON>
import json
from tqdm import tqdm


class Command(BaseCommand):
    help = "Closes the specified poll for voting"

    def add_arguments(self, parser):
        pass

    def handle(self, *args, **kwargs):
        with open('vpqh.json', 'rb') as f:
            users = json.load(f)

        for user in tqdm(users):
            u = User(
                email=user['username'],
                fullname=user['fullname']
            )
            u.set_password('VPQH@2o25')
            u.save()
            Quota.objects.create(user=u, package_id='BASIC')

