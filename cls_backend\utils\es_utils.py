from elasticsearch import Elasticsearch
from collections import defaultdict
from cls_backend.constants import VAN_BAN_CAN_CU, VAN_BAN_LIEN_QUAN_KHAC
import logging

logger = logging.getLogger("cls")

# load configs from env and return a es client
def init_es_client():
    try:
        from decouple import config
        endpoint = config('ELK_ENDPOINT', default='http://localhost:9200')
        es_user = config('ELK_USERNAME', default=None)
        es_password = config('ELK_PASSWORD', default=None)

        es = Elasticsearch(
            endpoint,
            http_auth=(es_user, es_password)
        )

        return es
    except Exception as e:
        print(f"Error initializing Elasticsearch client: {str(e)}")
        return None
    

def get_related_documents_from_es(
        document_id, 
        es_client, 
        index_name,
        include_fields=[]
    ):
    """
    Query Elasticsearch to get document related
    """

    body = {
        "query": {
            "term": {
                "ID": document_id
            }
        },
    }
    if include_fields:
        body['_source'] = include_fields

    logger.debug(f"ES query body: {body}")
    response = es_client.search(index=index_name, body=body)
    # logger.debug(response)
    hits = response.get('hits', {}).get('hits', [])
    # logger.debug(hits)
    
    if not hits:
        return None  # Không tìm thấy document gốc

    source = hits[0].get('_source', {})
    van_ban_lien_quan = source.get('van_ban_lien_quan', {})

    if not van_ban_lien_quan:
        return {VAN_BAN_CAN_CU: [], VAN_BAN_LIEN_QUAN_KHAC: []}
    doc_ids = set()
    doc_type_mapping = defaultdict(set)  # Mỗi doc_id có thể thuộc nhiều loại

    for doc_type, related_docs in van_ban_lien_quan.items():
        logger.debug(f"Processing doc_type: {doc_type} with {len(related_docs)} documents")
        for related_doc in related_docs:
            if isinstance(related_doc, dict) and 'id' in related_doc:
                doc_id = related_doc['id']
            elif isinstance(related_doc, int):
                doc_id = related_doc
            else:
                continue

            doc_ids.add(doc_id)
            doc_type_mapping[doc_id].add(doc_type)  # Thêm doc_type vào danh sách

    # Elasticsearch query
    body = {
        "query": {
            "terms": {
                "ID": list(doc_ids)
            }
        },
        "size": 10000
    }
    if include_fields:
        body['_source'] = include_fields

    related_resp = es_client.search(index=index_name, body=body)
    related_hits = related_resp.get('hits', {}).get('hits', [])

    response_data = defaultdict(list)

    for related_doc in related_hits:
        related_source = related_doc.get('_source', {})
        related_id = related_source.get('ID')

        doc_data = {
            "ID": related_source.get('ID'),
            "vbpl_id": related_source.get('vbpl_id'),
            "url": related_source.get('url'),
            "score": related_source.get('score'),
            "title": related_source.get('title'),
            # "toan_van": related_source.get('toan_van'),
            "don_vi": related_source.get('don_vi', []),
            "so_hieu": related_source.get('so_hieu'),
            "nguoi_ky": related_source.get('nguoi_ky'),
            "trich_yeu": related_source.get('trich_yeu'),
            "loai_van_ban": related_source.get('loai_van_ban'),
            "ngay_ban_hanh": related_source.get('ngay_ban_hanh'),
            "co_quan_ban_hanh": related_source.get('co_quan_ban_hanh'),
            "ngay_co_hieu_luc": related_source.get('ngay_co_hieu_luc'),
            "tinh_trang_hieu_luc": related_source.get('tinh_trang_hieu_luc'),
            "noi_dung": related_source.get('noi_dung'),
        }

        # Lặp qua tất cả loại liên quan mà văn bản này có
        for doc_type in doc_type_mapping.get(related_id, [VAN_BAN_LIEN_QUAN_KHAC]):
            response_data[doc_type].append(doc_data)

    return response_data
