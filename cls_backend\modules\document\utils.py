import requests
import json
from decouple import config

from cls_backend.constants import REQUEST_TIME_OUT


def find_document_ai(file, rerun):
    url = config('CLS_HOST') + "/joint_analyze/upload"

    payload = {}
    if not rerun:
        files = [
            ('file', (file.name, file.file, file.content_type))
        ]
        response = requests.post(url, data=payload, files=files, timeout=REQUEST_TIME_OUT)
    else:
        with open(file, 'rb') as f:
            files = [
                ('file', (f.name, f.read(), 'application/json'))
            ]
            response = requests.post(url, data=payload, files=files, timeout=REQUEST_TIME_OUT)

    return response.json()


def compare_ai(doc_old, doc_new):
    url = config('CLS_HOST') + "/compare/documents"
    payload = {
        'old_document': json.dumps(doc_old),
        'new_document': json.dumps(doc_new)
    }

    response = requests.request("POST", url, data=payload, timeout=REQUEST_TIME_OUT)
    return response
