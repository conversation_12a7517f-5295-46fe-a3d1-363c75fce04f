# Generated by Django 4.2 on 2025-05-13 06:52

from django.db import migrations, models
import django.db.models.deletion


class Migration(migrations.Migration):

    dependencies = [
        ("cls_backend", "0002_remove_document_task_id"),
    ]

    operations = [
        migrations.CreateModel(
            name="ThamQuyenNoiDung",
            fields=[
                (
                    "id",
                    models.BigAutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                ("created_at", models.DateTimeField(auto_now_add=True)),
                ("updated_at", models.DateTimeField(auto_now=True)),
                ("deleted_at", models.DateTimeField(blank=True, null=True)),
                (
                    "co_quan_co_tham_quyen",
                    models.CharField(blank=True, max_length=255, null=True),
                ),
                ("ket_qua", models.CharField(blank=True, max_length=255, null=True)),
                ("ly_do", models.Cha<PERSON><PERSON><PERSON>(blank=True, max_length=255, null=True)),
                (
                    "lawclause",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.CASCADE,
                        to="cls_backend.lawclause",
                    ),
                ),
                (
                    "related_clauses",
                    models.ManyToManyField(
                        blank=True,
                        null=True,
                        related_name="tqnd_related_clauses",
                        to="cls_backend.lawclause",
                    ),
                ),
            ],
            options={
                "abstract": False,
            },
        ),
    ]
