import os
import zipfile
from typing import NoReturn, <PERSON><PERSON>


def docx_to_xml(docx_path: str, output_dir: str):
    z = zipfile.ZipFile(docx_path)

    document_path = export_text_xml_to_json(z, output_dir)
    relations_path = export_relation_xml_to_json(z, output_dir)
    numberings_path = export_numbering_xml_to_json(z, output_dir)

    export_image_xml_to_json(z, output_dir)
    return document_path, relations_path, numberings_path


def export_text_xml_to_json(zipfile: str, output_dir: str) -> str:
    xmlfile = "word/document.xml"
    zipfile.extract(xmlfile, os.path.join(output_dir))
    return os.path.join(output_dir, xmlfile)


def export_relation_xml_to_json(zipfile: str, output_dir: str) -> str:
    xmlfile = "word/_rels/document.xml.rels"
    zipfile.extract(xmlfile, os.path.join(output_dir))
    return os.path.join(output_dir, xmlfile)


def export_numbering_xml_to_json(zipfile: str, output_dir: str) -> str:
    # xmlfile = "word/numbering.xml"
    # zipfile.extract(xmlfile, os.path.join(output_dir))
    # return os.path.join(output_dir, xmlfile)
    return


# def export_xml_to_json(zipfile, xmlfile, output_path):
#     content = zipfile.open(xmlfile).read()

#     Path(output_path).parent.mkdir(parents=True, exist_ok=True)
#     with open(output_path, "w") as f:
#         content = xmltodict.parse(content)
#         json.dump(content, f, indent=4, ensure_ascii=False)

def export_image_xml_to_json(zipfile: str, output_dir: str) -> NoReturn:
    images = filter(lambda x: x.startswith("word/media/"), zipfile.namelist())
    for image in images:
        zipfile.extract(image, output_dir)
