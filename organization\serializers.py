from rest_framework import serializers

from organization.organization_member.serializers import OrganizationMemberSerializer
from .models import Organization, OrganizationMember


class OrganizationSerializer(serializers.ModelSerializer):
    count = serializers.SerializerMethodField(read_only=True)

    class Meta:
        model = Organization
        fields = ['id', 'name', 'description', 'parent_organization', 'created_at', 'updated_at', 'represent_people', 'address', 'phone', 'email', 'status', 'count']
        read_only_fields = ['id', 'created_at', 'updated_at']

    def get_count(self, obj):
        # Đếm chính nó và tất cả các organization con (đệ quy)
        def count_recursive(org):
            children = Organization.objects.filter(parent_organization=org)
            total = 1  # chính nó
            for child in children:
                total += count_recursive(child)
            return total

        # Nếu context có queryset, trả về tổng số tất cả các tổ chức trong queryset (không đệ quy)
        queryset = self.context.get('queryset')
        if queryset is not None:
            return queryset.count()
        # <PERSON><PERSON><PERSON> không, tr<PERSON> về tổng số tổ chức liên quan đến obj (bao gồ<PERSON> cả các con đệ quy)
        return count_recursive(obj)

class OrganizationDetailSerializer(serializers.ModelSerializer):
    members = OrganizationMemberSerializer(many=True, read_only=True)
    member_count = serializers.SerializerMethodField()

    class Meta:
        model = Organization
        fields = ['id', 'name', 'description', 'members', 'member_count', 'created_at', 'updated_at']
        read_only_fields = ['id', 'created_at', 'updated_at']

    def get_member_count(self, obj):
        return obj.members.filter(is_active=True).count()