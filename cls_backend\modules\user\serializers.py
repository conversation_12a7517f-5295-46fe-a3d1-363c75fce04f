from rest_framework import serializers

from cls_backend.models import Position, User
from cls_backend.utils.file_utils import generate_presigned_url

class UserSerializer(serializers.ModelSerializer):
    password = serializers.CharField(required=False)
    email = serializers.EmailField(required=False)

    class Meta:
        model = User
        fields = ['id', 'email', 'password', 'avatar', 'fullname', 'is_staff', 'phone', 'position', 'gender', 'dob', 'address']
        read_only_fields = ['email', 'password']


    def get_queryset(self):
        queryset = Position.objects.all()
        organization_id = self.request.query_params.get('organization')
        if organization_id:
            queryset = queryset.filter(organization_id=organization_id)
        return queryset
    
    def create(self, validated_data):
        return User.objects.create_user(**validated_data)

    def update(self, instance, validated_data):
        # Không cho update email và password qua API này
        validated_data.pop('email', None)
        validated_data.pop('password', None)
        return super().update(instance, validated_data)

    def to_representation(self, instance):
        data = super().to_representation(instance)
        if instance.is_superuser:
            data['role'] = 'SUPER_ADMIN'
        elif instance.is_staff:
            data['role'] = 'ADMIN'
        else:
            data['role'] = 'USER'
        data.pop('is_staff')
        # Tạo presigned URL cho avatar nếu có
        if instance.avatar:
            avatar_url = generate_presigned_url(instance.avatar.name, expiration=3600)
            data['avatar'] = avatar_url
        else:
            data['avatar'] = None
        # Trả về toàn bộ thông tin của position (id, title)
        if instance.position:
            data['position'] = {
                'id': instance.position.id,
                'title': instance.position.title
            }
        else:
            data['position'] = None
        return data

