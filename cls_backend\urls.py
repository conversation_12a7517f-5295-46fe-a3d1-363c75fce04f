from django.urls import include
from django.urls import path
from django.contrib import admin
from rest_framework_simplejwt.views import TokenRefreshView
urlpatterns = [
    # version 1
    path('admin', admin.site.urls),
    path('auth/', include('cls_backend.modules.auth.urls')),
    # path('document/', include('cls_backend.modules.document.urls')),
    path('ocr/', include('cls_backend.modules.ocr.urls')),
    path('legal_search/', include('cls_backend.modules.legal_search.urls')),
    # path('activity/', include('cls_backend.modules.activity.urls')),
    path('conflict/', include('cls_backend.modules.conflict.urls')),
    path('workspace/', include('cls_backend.modules.workspace.urls')),
    # path('utils/keywords/', include('cls_backend.modules.keywords.urls')),

    # version 2
    path('api/v2/backend/auth/', include('cls_backend.modules.auth.urls')),
    path('api/v2/backend/position/', include('cls_backend.modules.position.urls')),
    path('api/v2/backend/document/', include('cls_backend.modules.document.urls')),
    path('api/v2/backend/workspace/', include('cls_backend.modules.workspace.urls')),
    path('api/v2/backend/note/', include('cls_backend.modules.note.urls')),
    path('api/v2/backend/ocr/', include('cls_backend.modules.ocr.urls')),
    path('api/v2/backend/legal_search/', include('cls_backend.modules.legal_search.urls')),
    # path('api/v2/backend/activity/', include('cls_backend.modules.activity.urls')),
    path('api/v2/backend/conflict/', include('cls_backend.modules.conflict.urls')),
    # path('api/v2/backend/utils/keywords/', include('cls_backend.modules.keywords.urls')),
    path('api/v2/backend/legal_modification/', include('cls_backend.modules.legal_modification.urls')),
    path('api/v2/backend/export/', include('cls_backend.modules.export.urls')),
    path('api/v2/backend/package/', include('cls_backend.modules.package.urls')),
    path('api/v2/backend/report/', include('cls_backend.modules.report.urls')),
    path("api/v2/backend/refresh-token/", TokenRefreshView.as_view()),
    path('api/v2/backend/chatbot/', include('chatbot.urls')),
    path('api/v2/backend/permission/', include('organization.urls')),
    path('api/v2/backend/dashboard/', include('dashboard.urls')),
    path('api/v2/backend/summarize/', include('cls_backend.modules.summarize.urls')),
    path('api/v2/backend/user/', include('cls_backend.modules.user.urls')),
    path('api/v2/backend/position/', include('cls_backend.modules.position.urls')),
    path('api/v2/maintainence/', include('maintainence.urls')),
]
