AGENCY_NAME_MAPPING = {
    "HĐND tỉnh Hậu Giang": "H<PERSON><PERSON> thành phố Cần Thơ",
    "HĐND tỉnh Sóc Trăng": "HĐND thành phố Cần Thơ",
    "HĐND tỉnh Hải Dương": "HĐND thành phố Hải Phòng",
    "HĐND tỉnh Quảng Nam":"HĐND thành phố Đà Nẵng",
    "HĐND tỉnh Bà Rịa - Vũng Tàu": "HĐND thành phố Hồ Chí Minh",
    "HĐND tỉnh Bình Dương": "HĐND thành phố Hồ Chí Minh",
    "HĐND tỉnh Kiên Giang": "HĐND tỉnh An Giang",
    "HĐND tỉnh Bắc Giang": "HĐND tỉnh Bắc Ninh",
    "HĐND tỉnh Bạc Liêu": "HĐND tỉnh Cà Mau",
    "HĐND tỉnh Phú Yên": "HĐND tỉnh Đắk Lắk",
    "HĐND tỉnh Bình Phước": "HĐND tỉnh Đồng Nai",
    "HĐND tỉnh Tiền Giang": "HĐND tỉnh Đồng Tháp",
    "HĐND tỉnh Bình Định": "HĐND tỉnh Gia Lai",
    "HĐND tỉnh Thái Bình": "HĐND tỉnh Hưng Yên",
    "HĐND tỉnh Nam Định": "HĐND tỉnh Ninh Bình",
    "HĐND tỉnh Hà Nam": "HĐND tỉnh Ninh Bình",
    "HĐND tỉnh Yên Bái":"HĐND tỉnh Lào Cai",
    "HĐND tỉnh Hà Giang": "HĐND tỉnh Tuyên Quang",
    "HĐND tỉnh Bình Thuận":"HĐND tỉnh Lâm Đồng",
    "HĐND tỉnh Đắk Nông":"HĐND tỉnh Lâm Đồng",
    "HĐND tỉnh Bến Tre": "HĐND tỉnh Vĩnh Long",
    "HĐND tỉnh Long An": "HĐND tỉnh Tây Ninh",
    "HĐND tỉnh Bắc Kạn": "HĐND tỉnh Thái Nguyên",
    "HĐND tỉnh Quảng Bình": "HĐND tỉnh Quảng Trị",
    "HĐND tỉnh Kon Tum": "HĐND tỉnh Quảng Ngãi",
    "HĐND tỉnh Vĩnh Phúc": "HĐND tỉnh Phú Thọ",
    "HĐND tỉnh Hòa Bình": "HĐND tỉnh Phú Thọ",
    "HĐND tỉnh Trà Vinh": "HĐND tỉnh Vĩnh Long",
    
    "UBND tỉnh Hậu Giang": "UBND thành phố Cần Thơ",
    "UBND tỉnh Sóc Trăng": "UBND thành phố Cần Thơ",
    "UBND tỉnh Hải Dương": "UBND thành phố Hải Phòng",
    "UBND tỉnh Quảng Nam":"UBND thành phố Đà Nẵng",
    "UBND tỉnh Bà Rịa - Vũng Tàu": "UBND thành phố Hồ Chí Minh",
    "UBND tỉnh Bình Dương": "UBND thành phố Hồ Chí Minh",
    "UBND tỉnh Kiên Giang": "UBND tỉnh An Giang",
    "UBND tỉnh Bắc Giang": "UBND tỉnh Bắc Ninh",
    "UBND tỉnh Bạc Liêu": "UBND tỉnh Cà Mau",
    "UBND tỉnh Phú Yên": "UBND tỉnh Đắk Lắk",
    "UBND tỉnh Bình Phước": "UBND tỉnh Đồng Nai",
    "UBND tỉnh Tiền Giang": "UBND tỉnh Đồng Tháp",
    "UBND tỉnh Bình Định": "UBND tỉnh Gia Lai",
    "UBND tỉnh Thái Bình": "UBND tỉnh Hưng Yên",
    "UBND tỉnh Nam Định": "UBND tỉnh Ninh Bình",
    "UBND tỉnh Hà Nam": "UBND tỉnh Ninh Bình",
    "UBND tỉnh Yên Bái":"UBND tỉnh Lào Cai",
    "UBND tỉnh Hà Giang": "UBND tỉnh Tuyên Quang",
    "UBND tỉnh Bình Thuận":"UBND tỉnh Lâm Đồng",
    "UBND tỉnh Đắk Nông":"UBND tỉnh Lâm Đồng",
    "UBND tỉnh Bến Tre": "UBND tỉnh Vĩnh Long",
    "UBND tỉnh Long An": "UBND tỉnh Tây Ninh",
    "UBND tỉnh Bắc Kạn": "UBND tỉnh Thái Nguyên",
    "UBND tỉnh Quảng Bình": "UBND tỉnh Quảng Trị",
    "UBND tỉnh Kon Tum": "UBND tỉnh Quảng Ngãi",
    "UBND tỉnh Vĩnh Phúc": "UBND tỉnh Phú Thọ",
    "UBND tỉnh Hòa Bình": "UBND tỉnh Phú Thọ",
    "UBND tỉnh Trà Vinh": "UBND tỉnh Vĩnh Long",
    "Bộ Tài nguyên và Môi trường":"Bộ Nông nghiệp và Môi trường",
    "Bộ Giao thông vận tải":"Bộ Xây dựng",
    "Bộ Kế hoạch và Đầu tư":"Bộ Tài chính",
    "Bộ Lao động - Thương binh và Xã hội":"Bộ Nội vụ",
    "Bộ Nông nghiệp và Phát triển nông thôn":"Bộ Nông nghiệp và Môi trường",
    "Bộ Thông tin và Truyền thông":"Bộ Khoa học và Công nghệ",
}

def convert_agency_name(old_name: str) -> str:
    """
    Chuyển đổi tên HĐND/UBND từ tỉnh thành cũ sang mới.

    Args:
        old_name (str): Tên cơ quan cần chuyển đổi.

    Returns:
        str: Tên cơ quan mới nếu có, ngược lại giữ nguyên.
    """
    # Kiểm tra nếu old_name là None hoặc không phải string
    if old_name is None:
        return ""

    # Đảm bảo old_name là string trước khi gọi strip()
    old_name_str = str(old_name).strip()
    return AGENCY_NAME_MAPPING.get(old_name_str, old_name_str)