from django.db import models
from django.utils import timezone
from django.contrib.auth import get_user_model
import uuid
from .constants import *


User = get_user_model()

class MaintenancePlan(models.Model):

    id = models.UUIDField(primary_key=True, default=uuid.uuid4, editable=False)
    title = models.CharField(max_length=255)
    description = models.TextField()
    # version = models.CharField(max_length=50)
    planned_start_time = models.DateTimeField()
    planned_end_time = models.DateTimeField()
    actual_start_time = models.DateTimeField(null=True, blank=True)
    actual_end_time = models.DateTimeField(null=True, blank=True)
    status = models.CharField(max_length=20, choices=STATUS_CHOICES, default=STATUS_SCHEDULED)
    # priority = models.CharField(max_length=20, choices=PRIORITY_CHOICES, default='MEDIUM')
    
    # Features and changes
    new_features = models.TextField(blank=True)
    bug_fixes = models.TextField(blank=True)
    security_updates = models.TextField(blank=True)
    
    # Rollback plan
    # rollback_plan = models.TextField()
    # rollback_tested = models.BooleanField(default=False)
    
    # Notifications
    notify_users = models.BooleanField(default=True)
    notification_message = models.TextField(blank=True)
    
    # Tracking
    # created_by = models.ForeignKey(User, on_delete=models.SET_NULL, null=True, blank=True)
    # updated_by = models.ForeignKey(User, on_delete=models.SET_NULL, null=True, blank=True)
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)
    deleted_at = models.DateTimeField(null=True, blank=True)

    def soft_delete(self):
        self.deleted_at = timezone.now()
        self.save()

    def __str__(self):
        return f"{self.title} - ({self.status}) from {self.planned_start_time} to {self.planned_end_time}"

    class Meta:
        ordering = ['-planned_start_time']