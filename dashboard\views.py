from django.shortcuts import render
from rest_framework.views import APIView
from rest_framework.response import Response
from rest_framework.permissions import IsAuthenticated
from rest_framework import status
from django.utils.dateparse import parse_datetime
from chatbot.constants import USER_MESSAGE
from chatbot.models import ChatMessage, Conversation
from dashboard.constant import DECREASE, INCREASE, NO_CHANGE
from organization.models import Organization, OrganizationMember
from cls_backend.models import User, Document
from django.db import models
from request_log.models import RequestLog
from organization.constants import ADMIN
from datetime import datetime, timedelta
from cls_backend.utils.es_utils import init_es_client
from decouple import config
from cls_backend.constants import (
    QUYET_DINH, THONG_TU, NGHI_QUYET, NGHI_DINH, THONG_TU_LIEN_TICH,
    LUAT, VAN_BAN_HOP_NHAT, PHAP_LENH, CONG_VAN, BO_LUAT,
    NGHI_QUYET_LIEN_TICH, CHI_THI, VAN_BAN_KHAC, LEN<PERSON>, HIE<PERSON>_PHAP,
    VAN_BAN_LIEN_QUAN, THONG_BAO, CHUONG_TRINH, SAC_LENH, THONG_TU_LIEN_BO,
    HIEP_DINH, SAC_LUAT, BAO_CAO, CONG_DIEN, DIEU_UOC_QUOC_TE,
    HUONG_DAN, KE_HOACH, VAN_BAN_WTO, DU_THAO
)

# Create your views here.

class DashboardAPIView(APIView):
    permission_classes = [IsAuthenticated]

    def get(self, request, *args, **kwargs):
        organization_id = request.query_params.get('organization_id')
        from_date = request.query_params.get('from_date')
        to_date = request.query_params.get('to_date')
        config = request.query_params.get('config', 'day')  # Mặc định là 'day'
        user_id = request.user.id
        user = request.user

        # Parse from_date và to_date
        from_date_dt = parse_datetime(from_date) if from_date else None
        to_date_dt = parse_datetime(to_date) if to_date else None
        prev_new_users = None
        percent_change = None
        prev_total_requests = None
        percent_change_requests = None
        change_type_requests = None
        change_amount_requests = None
        organizations_data = []
        active_users = None
        prev_active_users = None
        change_type_active_users = None
        change_amount_active_users = None
        if from_date_dt and to_date_dt:
            period = to_date_dt - from_date_dt
            prev_from_date_dt = from_date_dt - period
            prev_to_date_dt = from_date_dt
        else:
            prev_from_date_dt = None
            prev_to_date_dt = None

        # Danh sách các feature_name được phép
        ALLOWED_FEATURE_NAMES = ['legal_search', 'note_create', 'document_create', 'search_law_clauses', 'compare', 'create_workspace']

        if user.is_superuser:
            if not organization_id:
                total_users = User.objects.all().count()
                new_users = User.objects.filter(created_time__gte=from_date, created_time__lte=to_date).count()
                total_requests = ChatMessage.objects.filter(type=USER_MESSAGE).count() + RequestLog.objects.filter(feature_name__in=ALLOWED_FEATURE_NAMES).count()
                total_conversations = Conversation.objects.all().count()
                active_users = RequestLog.objects.filter(
                    timestamp__gte=from_date_dt, timestamp__lte=to_date_dt
                ).values('user_id').distinct().count() if from_date_dt and to_date_dt else 0
                
                # Lấy thông tin tổ chức và tỉ lệ người dùng
                organizations_data = []
                organizations = Organization.objects.all()
                for org in organizations:
                    org_user_count = OrganizationMember.objects.filter(organization=org).count()
                    org_percentage = (org_user_count / total_users * 100) if total_users > 0 else 0
                    organizations_data.append({
                        'organization_name': org.name,
                        'user_count': org_user_count,
                        'percentage': round(org_percentage, 2)
                    })
                
                if prev_from_date_dt and prev_to_date_dt:
                    prev_new_users = User.objects.filter(created_time__gte=prev_from_date_dt, created_time__lt=prev_to_date_dt).count()
                    prev_requests = ChatMessage.objects.filter(created_at__gte=prev_from_date_dt, created_at__lt=prev_to_date_dt, type=USER_MESSAGE).count() + \
                        RequestLog.objects.filter(timestamp__gte=prev_from_date_dt, timestamp__lt=prev_to_date_dt, feature_name__in=ALLOWED_FEATURE_NAMES).count()
                    prev_conversations = Conversation.objects.filter(created_at__gte=prev_from_date_dt, created_at__lt=prev_to_date_dt).count()
                    prev_active_users = RequestLog.objects.filter(
                        timestamp__gte=prev_from_date_dt, timestamp__lt=prev_to_date_dt
                    ).values('user_id').distinct().count()
            else:
                total_users = OrganizationMember.objects.filter(organization_id=organization_id).count()
                new_users = User.objects.filter(
                    organization_memberships__organization_id=organization_id,
                    created_time__gte=from_date, created_time__lte=to_date
                    ).count()
                
                total_requests = ChatMessage.objects.filter(
                    conversation__workspace__user__organization_memberships__organization_id=organization_id, type=USER_MESSAGE).count() + \
                    RequestLog.objects.filter(user__organization_memberships__organization_id=organization_id, feature_name__in=ALLOWED_FEATURE_NAMES).count()
                total_conversations = Conversation.objects.filter(
                    workspace__user__organization_memberships__organization_id=organization_id,created_at__gte=prev_from_date_dt, created_at__lt=prev_to_date_dt).count()
                active_users = RequestLog.objects.filter(
                    user__organization_memberships__organization_id=organization_id,
                    timestamp__gte=from_date_dt, timestamp__lte=to_date_dt
                ).values('user_id').distinct().count() if from_date_dt and to_date_dt else 0
                if prev_from_date_dt and prev_to_date_dt:
                    prev_new_users = User.objects.filter(
                        organization_memberships__organization_id=organization_id,
                        created_time__gte=prev_from_date_dt, created_time__lt=prev_to_date_dt
                    ).count()
                    prev_requests = ChatMessage.objects.filter(
                        conversation__workspace__user__organization_memberships__organization_id=organization_id,
                        created_at__gte=prev_from_date_dt, created_at__lt=prev_to_date_dt, type=USER_MESSAGE
                    ).count() + \
                        RequestLog.objects.filter(
                            user__organization_memberships__organization_id=organization_id,
                            timestamp__gte=prev_from_date_dt, timestamp__lt=prev_to_date_dt,
                            feature_name__in=ALLOWED_FEATURE_NAMES
                        ).count()
                    prev_conversations = Conversation.objects.filter(workspace__user__organization_memberships__organization_id=organization_id,created_at__gte=prev_from_date_dt, created_at__lt=prev_to_date_dt).count()
                    prev_active_users = RequestLog.objects.filter(
                        user__organization_memberships__organization_id=organization_id,
                        timestamp__gte=prev_from_date_dt, timestamp__lt=prev_to_date_dt
                    ).values('user_id').distinct().count()
        else:
            if organization_id and user.organization_memberships.filter(organization_id=organization_id, role=ADMIN).exists():      
                total_users =  OrganizationMember.objects.filter(organization_id=organization_id).count()
                new_users = User.objects.filter(
                    organization_memberships__organization_id=organization_id,
                    created_time__gte=from_date, created_time__lte=to_date
                    ).count()
                total_requests = ChatMessage.objects.filter(
                    conversation__workspace__user__organization_memberships__organization_id=organization_id,type = USER_MESSAGE).count() + \
                    RequestLog.objects.filter(user__organization_memberships__organization_id=organization_id, feature_name__in=ALLOWED_FEATURE_NAMES).count()
                total_conversations = Conversation.objects.filter(
                    workspace__user__organization_memberships__organization_id=organization_id,created_at__gte=prev_from_date_dt, created_at__lt=prev_to_date_dt).count()
                active_users = RequestLog.objects.filter(
                    user__organization_memberships__organization_id=organization_id,
                    timestamp__gte=from_date_dt, timestamp__lte=to_date_dt
                ).values('user_id').distinct().count() if from_date_dt and to_date_dt else 0
                if prev_from_date_dt and prev_to_date_dt:
                    prev_new_users = User.objects.filter(
                        organization_memberships__organization_id=organization_id,
                        created_time__gte=prev_from_date_dt, created_time__lt=prev_to_date_dt
                    ).count()
                    prev_requests = ChatMessage.objects.filter(
                        conversation__workspace__user__organization_memberships__organization_id=organization_id,
                        created_at__gte=prev_from_date_dt, created_at__lt=prev_to_date_dt,type = USER_MESSAGE
                    ).count() + \
                        RequestLog.objects.filter(
                            user__organization_memberships__organization_id=organization_id,
                            timestamp__gte=prev_from_date_dt, timestamp__lt=prev_to_date_dt,
                            feature_name__in=ALLOWED_FEATURE_NAMES
                        ).count()
                    prev_conversations = Conversation.objects.filter(workspace__user__organization_memberships__organization_id=organization_id,created_at__gte=prev_from_date_dt, created_at__lt=prev_to_date_dt).count()
                    prev_active_users = RequestLog.objects.filter(
                        user__organization_memberships__organization_id=organization_id,
                        timestamp__gte=prev_from_date_dt, timestamp__lt=prev_to_date_dt
                    ).values('user_id').distinct().count()
            else:
                return Response({"detail": "You do not have permission to access this data."}, status=status.HTTP_403_FORBIDDEN)

        # Tính phần trăm thay đổi new_users
        if prev_new_users is not None and prev_new_users != 0:
            percent_change_user = ((new_users - prev_new_users) / prev_new_users) * 100
        elif prev_new_users == 0:
            # percent_change_user = 100.0 if new_users > 0 else 0.0
            percent_change_user = 0.0

        # Xác định loại thay đổi và số lượng thay đổi tuyệt đối new_users
        change_type_user = None
        change_amount_user = None
        if prev_new_users is not None:
            change_amount_user = new_users - prev_new_users
            if change_amount_user > 0:
                change_type_user = INCREASE
            elif change_amount_user < 0:
                change_type_user = DECREASE
            else:
                change_type_user = NO_CHANGE

        # Tính phần trăm thay đổi total_requests (bao gồm cả ChatMessage và RequestLog với feature_name cụ thể)
        if prev_requests is not None and prev_requests != 0:
            percent_change_requests = ((total_requests - prev_requests) / prev_requests) * 100
        elif prev_requests == 0:
            percent_change_requests = 0.0

        # Xác định loại thay đổi và số lượng thay đổi tuyệt đối total_requests
        if prev_requests is not None:
            change_amount_requests = total_requests - prev_requests
            if change_amount_requests > 0:
                change_type_requests = INCREASE
            elif change_amount_requests < 0:
                change_type_requests = DECREASE
            else:
                change_type_requests = NO_CHANGE
        
        
        # Tính phần trăm thay đổi total_requests
        if prev_conversations is not None and prev_conversations != 0:
            percent_change_conversations = ((total_conversations - prev_conversations) / prev_conversations) * 100
        elif prev_conversations == 0:
            percent_change_conversations = 0.0

        # Xác định loại thay đổi và số lượng thay đổi tuyệt đối total_requests
        if prev_conversations is not None:
            change_amount_conversations = total_conversations - prev_conversations
            if change_amount_conversations > 0:
                change_type_conversations = INCREASE
            elif change_amount_conversations < 0:
                change_type_conversations = DECREASE
            else:
                change_type_conversations = NO_CHANGE
        
        # Tính change_type và change_amount cho active_users
        if prev_active_users is not None:
            change_amount_active_users = active_users - prev_active_users
            if change_amount_active_users > 0:
                change_type_active_users = INCREASE
            elif change_amount_active_users < 0:
                change_type_active_users = DECREASE
            else:
                change_type_active_users = NO_CHANGE


        # Chart data: trả về thông tin theo config (day, week, month, year)
        chart_new_users = []
        chart_new_conversations = []
        chart_active_users = []

        if from_date_dt and to_date_dt:
            chart_new_users = self._get_chart_data(
                from_date_dt, to_date_dt, config, organization_id, 'users'
            )
            chart_new_conversations = self._get_detailed_chart_data(
                from_date_dt, to_date_dt, config, organization_id
            )
            chart_active_users = self._get_chart_data(
                from_date_dt, to_date_dt, config, organization_id, 'active_users'
            )


        response_data = {
            'total_users': total_users,
            'new_users': new_users,
            'prev_new_users': prev_new_users,
            'percent_change': percent_change_user,
            'change_type': change_type_user,
            'change_amount': change_amount_user,
            'total_requests': total_requests,
            'prev_requests': prev_requests,
            'percent_change_requests': percent_change_requests,
            'change_type_requests': change_type_requests,
            'change_amount_requests': change_amount_requests,
            'total_conversations': total_conversations,
            'prev_conversations': prev_conversations,
            'percent_change_conversations': percent_change_conversations,
            'change_type_conversations': change_type_conversations,
            'change_amount_conversations': change_amount_conversations,
            'active_users': active_users,
            'prev_active_users': prev_active_users,
            'change_type_active_users': change_type_active_users,
            'change_amount_active_users': change_amount_active_users,
            'chart_new_users': chart_new_users,
            'chart_new_conversations': chart_new_conversations,
            'chart_active_users': chart_active_users
        }
        
        # Thêm thông tin tổ chức nếu là superuser và không có organization_id
        if user.is_superuser and not organization_id:
            response_data['organizations_data'] = organizations_data
        
        return Response(response_data)

    def _get_chart_data(self, from_date_dt, to_date_dt, config, organization_id, data_type):
        """
        Tạo dữ liệu chart theo config (day, week, month, year)
        data_type: 'users', 'conversations', 'active_users'
        """
        chart_data = []

        if config == 'day':
            # Logic theo ngày (giữ nguyên logic cũ)
            num_days = (to_date_dt.date() - from_date_dt.date()).days + 1
            for i in range(num_days):
                day = from_date_dt.date() + timedelta(days=i)
                day_start = datetime.combine(day, datetime.min.time())
                day_end = datetime.combine(day, datetime.max.time())

                count = self._get_count_for_period(day_start, day_end, organization_id, data_type)

                chart_data.append({
                    'date': day.strftime("%Y-%m-%d"),
                    'formatted_date': day.strftime("%d/%m/%Y"),
                    'count': count,
                })

        elif config == 'week':
            # Logic theo tuần
            current_date = from_date_dt.date()
            while current_date <= to_date_dt.date():
                # Tìm ngày đầu tuần (thứ 2)
                week_start = current_date - timedelta(days=current_date.weekday())
                week_end = week_start + timedelta(days=6)

                # Đảm bảo không vượt quá to_date
                if week_end > to_date_dt.date():
                    week_end = to_date_dt.date()

                week_start_dt = datetime.combine(week_start, datetime.min.time())
                week_end_dt = datetime.combine(week_end, datetime.max.time())

                count = self._get_count_for_period(week_start_dt, week_end_dt, organization_id, data_type)

                chart_data.append({
                    'date': f"{week_start.strftime('%Y-%m-%d')} - {week_end.strftime('%Y-%m-%d')}",
                    'formatted_date': f"{week_start.strftime('%d/%m')} - {week_end.strftime('%d/%m/%Y')}",
                    'count': count,
                })

                # Chuyển sang tuần tiếp theo
                current_date = week_end + timedelta(days=1)

        elif config == 'month':
            # Logic theo tháng
            current_date = from_date_dt.date().replace(day=1)  # Đầu tháng
            while current_date <= to_date_dt.date():
                # Tìm ngày cuối tháng
                if current_date.month == 12:
                    next_month = current_date.replace(year=current_date.year + 1, month=1)
                else:
                    next_month = current_date.replace(month=current_date.month + 1)
                month_end = next_month - timedelta(days=1)

                # Đảm bảo không vượt quá to_date
                if month_end > to_date_dt.date():
                    month_end = to_date_dt.date()

                month_start_dt = datetime.combine(current_date, datetime.min.time())
                month_end_dt = datetime.combine(month_end, datetime.max.time())

                count = self._get_count_for_period(month_start_dt, month_end_dt, organization_id, data_type)

                chart_data.append({
                    'date': current_date.strftime("%Y-%m"),
                    'formatted_date': current_date.strftime("%m/%Y"),
                    'count': count,
                })

                # Chuyển sang tháng tiếp theo
                current_date = next_month

        elif config == 'year':
            # Logic theo năm
            current_year = from_date_dt.year
            end_year = to_date_dt.year

            while current_year <= end_year:
                year_start = datetime(current_year, 1, 1)
                year_end = datetime(current_year, 12, 31, 23, 59, 59)

                # Đảm bảo không vượt quá khoảng thời gian được chọn
                if year_start < from_date_dt:
                    year_start = from_date_dt
                if year_end > to_date_dt:
                    year_end = to_date_dt

                count = self._get_count_for_period(year_start, year_end, organization_id, data_type)

                chart_data.append({
                    'date': str(current_year),
                    'formatted_date': str(current_year),
                    'count': count,
                })

                current_year += 1

        return chart_data

    def _get_count_for_period(self, start_dt, end_dt, organization_id, data_type):
        """
        Lấy số lượng cho một khoảng thời gian cụ thể
        """
        # Danh sách các feature_name được phép
        ALLOWED_FEATURE_NAMES = ['legal_search', 'note_create', 'document_create', 'search_law_clauses', 'compare', 'create_workspace']

        if data_type == 'users':
            if organization_id:
                return User.objects.filter(
                    organization_memberships__organization_id=organization_id,
                    created_time__gte=start_dt, created_time__lte=end_dt
                ).count()
            else:
                return User.objects.filter(
                    created_time__gte=start_dt, created_time__lte=end_dt
                ).count()

        elif data_type == 'conversations':
            # Tính tổng ChatMessage và RequestLog với feature_name cụ thể
            chat_count = 0
            request_count = 0

            if organization_id:
                chat_count = ChatMessage.objects.filter(
                    conversation__workspace__user__organization_memberships__organization_id=organization_id,
                    created_at__gte=start_dt, created_at__lte=end_dt,
                    type=USER_MESSAGE
                ).count()
                request_count = RequestLog.objects.filter(
                    user__organization_memberships__organization_id=organization_id,
                    timestamp__gte=start_dt, timestamp__lte=end_dt,
                    feature_name__in=ALLOWED_FEATURE_NAMES
                ).count()
            else:
                chat_count = ChatMessage.objects.filter(
                    created_at__gte=start_dt, created_at__lte=end_dt,
                    type=USER_MESSAGE
                ).count()
                request_count = RequestLog.objects.filter(
                    timestamp__gte=start_dt, timestamp__lte=end_dt,
                    feature_name__in=ALLOWED_FEATURE_NAMES
                ).count()

            return chat_count + request_count

        elif data_type == 'active_users':
            if organization_id:
                return RequestLog.objects.filter(
                    user__organization_memberships__organization_id=organization_id,
                    timestamp__gte=start_dt, timestamp__lte=end_dt
                ).values('user_id').distinct().count()
            else:
                return RequestLog.objects.filter(
                    timestamp__gte=start_dt, timestamp__lte=end_dt
                ).values('user_id').distinct().count()

        return 0

    def _get_detailed_chart_data(self, from_date_dt, to_date_dt, config, organization_id):
        """
        Tạo dữ liệu chart chi tiết bao gồm ChatMessage và RequestLog theo từng feature
        """
        ALLOWED_FEATURE_NAMES = ['legal_search', 'note_create', 'document_create', 'search_law_clauses', 'compare', 'create_workspace']
        chart_data = []

        if config == 'day':
            num_days = (to_date_dt.date() - from_date_dt.date()).days + 1
            for i in range(num_days):
                day = from_date_dt.date() + timedelta(days=i)
                day_start = datetime.combine(day, datetime.min.time())
                day_end = datetime.combine(day, datetime.max.time())

                # Lấy số lượng ChatMessage
                if organization_id:
                    chat_count = ChatMessage.objects.filter(
                        conversation__workspace__user__organization_memberships__organization_id=organization_id,
                        created_at__gte=day_start, created_at__lte=day_end,
                        type=USER_MESSAGE
                    ).count()
                else:
                    chat_count = ChatMessage.objects.filter(
                        created_at__gte=day_start, created_at__lte=day_end,
                        type=USER_MESSAGE
                    ).count()

                # Lấy số lượng RequestLog theo từng feature
                feature_counts = {}
                for feature in ALLOWED_FEATURE_NAMES:
                    if organization_id:
                        count = RequestLog.objects.filter(
                            user__organization_memberships__organization_id=organization_id,
                            timestamp__gte=day_start, timestamp__lte=day_end,
                            feature_name=feature
                        ).count()
                    else:
                        count = RequestLog.objects.filter(
                            timestamp__gte=day_start, timestamp__lte=day_end,
                            feature_name=feature
                        ).count()
                    feature_counts[feature] = count

                # Tính tổng
                total_requests = sum(feature_counts.values())
                total_count = chat_count + total_requests

                chart_data.append({
                    'date': day.strftime("%Y-%m-%d"),
                    'formatted_date': day.strftime("%d/%m/%Y"),
                    'chat_messages': chat_count,
                    'feature_counts': feature_counts,
                    'total_requests': total_requests,
                    'count': total_count,
                })

        elif config == 'week':
            current_date = from_date_dt.date()
            while current_date <= to_date_dt.date():
                week_start = current_date - timedelta(days=current_date.weekday())
                week_end = week_start + timedelta(days=6)

                if week_end > to_date_dt.date():
                    week_end = to_date_dt.date()

                week_start_dt = datetime.combine(week_start, datetime.min.time())
                week_end_dt = datetime.combine(week_end, datetime.max.time())

                # Lấy số lượng ChatMessage
                if organization_id:
                    chat_count = ChatMessage.objects.filter(
                        conversation__workspace__user__organization_memberships__organization_id=organization_id,
                        created_at__gte=week_start_dt, created_at__lte=week_end_dt,
                        type=USER_MESSAGE
                    ).count()
                else:
                    chat_count = ChatMessage.objects.filter(
                        created_at__gte=week_start_dt, created_at__lte=week_end_dt,
                        type=USER_MESSAGE
                    ).count()

                # Lấy số lượng RequestLog theo từng feature
                feature_counts = {}
                for feature in ALLOWED_FEATURE_NAMES:
                    if organization_id:
                        count = RequestLog.objects.filter(
                            user__organization_memberships__organization_id=organization_id,
                            timestamp__gte=week_start_dt, timestamp__lte=week_end_dt,
                            feature_name=feature
                        ).count()
                    else:
                        count = RequestLog.objects.filter(
                            timestamp__gte=week_start_dt, timestamp__lte=week_end_dt,
                            feature_name=feature
                        ).count()
                    feature_counts[feature] = count

                total_requests = sum(feature_counts.values())
                total_count = chat_count + total_requests

                chart_data.append({
                    'date': f"{week_start.strftime('%Y-%m-%d')} - {week_end.strftime('%Y-%m-%d')}",
                    'formatted_date': f"{week_start.strftime('%d/%m')} - {week_end.strftime('%d/%m/%Y')}",
                    'chat_messages': chat_count,
                    'feature_counts': feature_counts,
                    'total_requests': total_requests,
                    'count': total_count,
                })

                current_date = week_end + timedelta(days=1)

        elif config == 'month':
            current_date = from_date_dt.date().replace(day=1)
            while current_date <= to_date_dt.date():
                if current_date.month == 12:
                    next_month = current_date.replace(year=current_date.year + 1, month=1)
                else:
                    next_month = current_date.replace(month=current_date.month + 1)
                month_end = next_month - timedelta(days=1)

                if month_end > to_date_dt.date():
                    month_end = to_date_dt.date()

                month_start_dt = datetime.combine(current_date, datetime.min.time())
                month_end_dt = datetime.combine(month_end, datetime.max.time())

                # Lấy số lượng ChatMessage
                if organization_id:
                    chat_count = ChatMessage.objects.filter(
                        conversation__workspace__user__organization_memberships__organization_id=organization_id,
                        created_at__gte=month_start_dt, created_at__lte=month_end_dt,
                        type=USER_MESSAGE
                    ).count()
                else:
                    chat_count = ChatMessage.objects.filter(
                        created_at__gte=month_start_dt, created_at__lte=month_end_dt,
                        type=USER_MESSAGE
                    ).count()

                # Lấy số lượng RequestLog theo từng feature
                feature_counts = {}
                for feature in ALLOWED_FEATURE_NAMES:
                    if organization_id:
                        count = RequestLog.objects.filter(
                            user__organization_memberships__organization_id=organization_id,
                            timestamp__gte=month_start_dt, timestamp__lte=month_end_dt,
                            feature_name=feature
                        ).count()
                    else:
                        count = RequestLog.objects.filter(
                            timestamp__gte=month_start_dt, timestamp__lte=month_end_dt,
                            feature_name=feature
                        ).count()
                    feature_counts[feature] = count

                total_requests = sum(feature_counts.values())
                total_count = chat_count + total_requests

                chart_data.append({
                    'date': current_date.strftime("%Y-%m"),
                    'formatted_date': current_date.strftime("%m/%Y"),
                    'chat_messages': chat_count,
                    'feature_counts': feature_counts,
                    'total_requests': total_requests,
                    'count': total_count,
                })

                current_date = next_month

        elif config == 'year':
            current_year = from_date_dt.year
            end_year = to_date_dt.year

            while current_year <= end_year:
                year_start = datetime(current_year, 1, 1)
                year_end = datetime(current_year, 12, 31, 23, 59, 59)

                if year_start < from_date_dt:
                    year_start = from_date_dt
                if year_end > to_date_dt:
                    year_end = to_date_dt

                # Lấy số lượng ChatMessage
                if organization_id:
                    chat_count = ChatMessage.objects.filter(
                        conversation__workspace__user__organization_memberships__organization_id=organization_id,
                        created_at__gte=year_start, created_at__lte=year_end,
                        type=USER_MESSAGE
                    ).count()
                else:
                    chat_count = ChatMessage.objects.filter(
                        created_at__gte=year_start, created_at__lte=year_end,
                        type=USER_MESSAGE
                    ).count()

                # Lấy số lượng RequestLog theo từng feature
                feature_counts = {}
                for feature in ALLOWED_FEATURE_NAMES:
                    if organization_id:
                        count = RequestLog.objects.filter(
                            user__organization_memberships__organization_id=organization_id,
                            timestamp__gte=year_start, timestamp__lte=year_end,
                            feature_name=feature
                        ).count()
                    else:
                        count = RequestLog.objects.filter(
                            timestamp__gte=year_start, timestamp__lte=year_end,
                            feature_name=feature
                        ).count()
                    feature_counts[feature] = count

                total_requests = sum(feature_counts.values())
                total_count = chat_count + total_requests

                chart_data.append({
                    'date': str(current_year),
                    'formatted_date': str(current_year),
                    'chat_messages': chat_count,
                    'feature_counts': feature_counts,
                    'total_requests': total_requests,
                    'count': total_count,
                })

                current_year += 1

        return chart_data


class ElasticLoaiVanBanStatsAPIView(APIView):
    permission_classes = [IsAuthenticated]

    def post(self, request, *args, **kwargs):
        # Danh sách các loại văn bản được phép
        ALLOWED_LOAI_VAN_BAN = {
            QUYET_DINH, THONG_TU, NGHI_QUYET, NGHI_DINH, THONG_TU_LIEN_TICH,
            LUAT, VAN_BAN_HOP_NHAT, PHAP_LENH, CONG_VAN, BO_LUAT,
            NGHI_QUYET_LIEN_TICH, CHI_THI, VAN_BAN_KHAC, LENH, HIEN_PHAP,
            VAN_BAN_LIEN_QUAN, THONG_BAO, CHUONG_TRINH, SAC_LENH, THONG_TU_LIEN_BO,
            HIEP_DINH, SAC_LUAT, BAO_CAO, CONG_DIEN, DIEU_UOC_QUOC_TE,
            HUONG_DAN, KE_HOACH, VAN_BAN_WTO, DU_THAO
        }

        es_client = init_es_client()
        if not es_client:
            return Response({"error": "Could not initialize Elasticsearch client."}, status=status.HTTP_500_INTERNAL_SERVER_ERROR)

        index_name = config('ELK_DOCUMENT_INDEX')
        body = {
            "size": 0,
            "query": {
                "bool": {
                    "must": [
                        {"term": {"type_of_van_ban": 1}}
                    ]
                }
            },
            "aggs": {
                "thong_ke_theo_loai_van_ban": {
                    "terms": {
                        "field": "loai_van_ban.keyword",
                        "size": 100
                    }
                }
            }
        }
        if request.data:
            body = request.data
        try:
            result = es_client.search(index=index_name, body=body)
            # Lấy dữ liệu bucket trong aggregations
            buckets = result.get('aggregations', {}).get('thong_ke_theo_loai_van_ban', {}).get('buckets', [])

            # Lọc chỉ lấy những bucket có key trong danh sách được phép
            filtered_buckets = [
                bucket for bucket in buckets
                if bucket.get('key') in ALLOWED_LOAI_VAN_BAN
            ]
            # Thứ tự mong muốn từ frontend
            PREFERRED_ORDER = [
                "Hiến pháp", "Bộ luật", "Luật", "Pháp lệnh", "Nghị quyết", "Nghị quyết liên tịch",
                "Quyết định", "Nghị định", "Thông tư", "Thông tư liên tịch", "Công văn", "Báo cáo",
                "Chỉ thị", "Công điện", "Điều ước quốc tế", "Hướng dẫn", "Kế hoạch", "Thông báo",
                "Văn bản hợp nhất", "Văn bản khác", "Văn bản WTO", "Dự thảo", "Lệnh", "Sắc lệnh"
            ]
            # Lọc bucket chỉ chứa loại nằm trong danh sách ưu tiên
            filtered_buckets = [
                bucket for bucket in buckets
                if bucket.get('key') in PREFERRED_ORDER
            ]

            # Tính tổng số lượng văn bản thuộc các loại được chọn
            total_doc_count = sum(bucket.get('doc_count', 0) for bucket in filtered_buckets)
            # Sắp xếp lại theo thứ tự mong muốn
            sorted_buckets = sorted(
                filtered_buckets,
                key=lambda x: PREFERRED_ORDER.index(x['key']) if x['key'] in PREFERRED_ORDER else len(PREFERRED_ORDER)
            )
            final_buckets = [{"key": "Tất cả", "doc_count": total_doc_count}] + sorted_buckets
            return Response(final_buckets, status=status.HTTP_200_OK)
        except Exception as e:
            return Response({"error": str(e)}, status=status.HTTP_500_INTERNAL_SERVER_ERROR)
















