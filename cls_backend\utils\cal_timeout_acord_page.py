from decouple import config
import os
import subprocess
import tempfile
import fitz  # PyMuPDF
from django.core.exceptions import ObjectDoesNotExist

from cls_backend.models import Document
from cls_backend.tasks.handle_ie import convert_docx_to_pdf_cross_platform



def download_file_to_temp(file_field):
    suffix = os.path.splitext(file_field.name)[1]
    with tempfile.NamedTemporaryFile(suffix=suffix, delete=False) as tmp:
        file_field.open()
        tmp.write(file_field.read())
        tmp.flush()
        return tmp.name

def count_pdf_pages(file_path):
    with fitz.open(file_path) as doc:
        return doc.page_count
    
def convert_doc_to_pdf(input_path, output_path=None):
    """
    Chuyển trực tiếp .doc hoặc .docx sang .pdf bằng LibreOffice
    """
    if not input_path.lower().endswith(('.doc', '.docx')):
        raise ValueError("Đầu vào phải là .doc hoặc .docx")

    output_dir = os.path.dirname(output_path) if output_path else os.path.dirname(input_path)
    # soffice_path = r"C:\Program Files\LibreOffice\program\soffice.exe"
    try:
        subprocess.run([
            # soffice_path, 
            "soffice",
            "--headless",
            "--convert-to", "pdf",
            "--outdir", output_dir,
            input_path
        ], check=True)
    except subprocess.CalledProcessError as e:
        raise RuntimeError(f"Lỗi chuyển file sang PDF: {e}")

    # Tạo đường dẫn file .pdf mới sinh ra
    base_name = os.path.splitext(os.path.basename(input_path))[0]
    converted_path = os.path.join(output_dir, base_name + ".pdf")

    if not os.path.exists(converted_path):
        raise FileNotFoundError("Không tìm thấy file PDF sau khi chuyển")

    return converted_path

def get_timeout_limit_from_document_id(document_id):
    """
    Trả về (soft_timeout, hard_timeout) dựa trên số trang tài liệu.
    soft_timeout = SOFT_TASK_TIMEOUT * số trang
    hard_timeout = soft_timeout + 30
    """
    try:
        document = Document.objects.get(id=document_id)
        if not document.origin or not document.origin.name:
            raise ValueError("Tài liệu không có file gốc.")

        local_path = download_file_to_temp(document.origin)
        suffix = os.path.splitext(local_path)[1].lower()

        if suffix == ".pdf":
            page_count = count_pdf_pages(local_path)
        elif suffix == ".docx":
            pdf_temp_path = local_path.replace(".docx", ".pdf")
            convert_docx_to_pdf_cross_platform(local_path, pdf_temp_path)
            page_count = count_pdf_pages(pdf_temp_path)
            os.remove(pdf_temp_path)
        elif suffix == ".doc":
            pdf_temp_path = local_path.replace(".doc", ".pdf")
            convert_doc_to_pdf(local_path, pdf_temp_path)
            page_count = count_pdf_pages(pdf_temp_path)
            os.remove(pdf_temp_path)
        else:
            raise ValueError(f"Định dạng file không hỗ trợ: {suffix}")

        soft_timeout = config('SOFT_TASK_TIMEOUT', cast=int, default=30) * page_count
        hard_timeout = soft_timeout + 30

        return soft_timeout, hard_timeout

    except ObjectDoesNotExist:
        raise ValueError(f"Không tìm thấy document với id {document_id}")
    finally:
        try:
            if 'local_path' in locals() and os.path.exists(local_path):
                os.remove(local_path)
        except Exception as e:
            pass  # hoặc log lỗi nếu cần
