import json
import pandas as pd
import io
import re
from datetime import datetime
from bs4 import BeautifulSoup
import logging
import requests
from decouple import config
from django.http import HttpResponse
from rest_framework import status
from rest_framework.permissions import IsAuthenticated
from rest_framework.response import Response
from rest_framework.views import APIView

from cls_backend.constants import REQUEST_TIME_OUT

logger = logging.getLogger("cls")
class ExportView(APIView):
    permission_classes = [IsAuthenticated]

    def post(self, request):
            try:
                data = json.loads(request.body)
                processed_data = []

                # Lấy danh sách item
                if isinstance(data, list):
                    items = data
                elif isinstance(data, dict) and 'data' in data and isinstance(data['data'], list):
                    items = data['data']
                else:
                    items = [data]

                has_term_content = False  # Đánh dấu nếu có ít nhất một nội dung

                for index, item in enumerate(items, 1):
                    # Xử lý trích yếu
                    trich_yeu = item.get('trich_yeu', '')
                    trich_yeu_clean = BeautifulSoup(trich_yeu, 'html.parser').get_text() if trich_yeu else ''

                    terms = item.get('terms', '')
                    

                    if terms:  # Nếu có nội dung thì thêm 2 cột
                        has_term_content = True

                    processed_item = {
                        # 'STT': index,
                        'Loại văn bản': item.get('loai_van_ban', ''),
                        'Số hiệu': item.get('so_hieu', ''),
                        'Trích yếu': trich_yeu_clean,
                    }

                    if has_term_content:
                        for term in terms:
                            term_item = processed_item.copy()  # Create a new copy for each term
                            term_item['Vị trí'] = term.get('position', '')
                            term_item['Nội dung'] = term.get('term_content', '')
                            processed_data.append(term_item)   
                    else:
                        processed_data.append(processed_item) 

                # Nếu không có nội dung nào, xóa toàn bộ cột "Vị trí" và "Nội dung"
                for idx, row in enumerate(processed_data, start=1):
                    row['STT'] = idx
                columns = ['STT'] + [col for col in processed_data[0].keys() if col != 'STT']
                
                if not has_term_content:
                    for row in processed_data:
                        row.pop('Vị trí', None)
                        row.pop('Nội dung', None)

                # Tạo DataFrame và Excel
                df = pd.DataFrame(processed_data)[columns]
                # df = pd.DataFrame(processed_data)
                buffer = io.BytesIO()

                with pd.ExcelWriter(buffer, engine='xlsxwriter') as writer:
                    df.to_excel(writer, sheet_name='Văn bản', index=False)

                    workbook = writer.book
                    worksheet = writer.sheets['Văn bản']

                    # Header format
                    header_format = workbook.add_format({
                        'bold': True,
                        'text_wrap': True,
                        'valign': 'top',
                        'fg_color': '#D7E4BC',
                        'border': 1
                    })

                    content_format = workbook.add_format({
                        'text_wrap': True,
                        'valign': 'top'
                    })

                    # Ghi header
                    for col_num, value in enumerate(df.columns.values):
                        worksheet.write(0, col_num, value, header_format)

                        # Đặt độ rộng hợp lý
                        if value == 'Loại văn bản' or value == 'Số hiệu':
                            worksheet.set_column(col_num, col_num, 20)
                        elif value == 'Trích yếu':
                            worksheet.set_column(col_num, col_num, 40)
                        elif value == 'Nội dung':
                            worksheet.set_column(col_num, col_num, 60)
                        elif value == 'Vị trí':
                            worksheet.set_column(col_num, col_num, 20)

                    # Ghi nội dung
                    for row_num in range(1, len(df) + 1):
                        for col_num in range(len(df.columns)):
                            worksheet.write(row_num, col_num, df.iloc[row_num - 1, col_num], content_format)

                buffer.seek(0)
                timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
                filename = f"van_ban_export_{timestamp}.xlsx"

                return HttpResponse(
                    buffer.getvalue(),
                    content_type='application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
                    headers={'Content-Disposition': f'attachment; filename="{filename}"'}
                )

            except Exception as e:
                logger.error(f"Error exporting Excel: {str(e)}")
                return Response({"error": str(e)}, status=status.HTTP_500_INTERNAL_SERVER_ERROR)

class ExportThamQuyenHinhThucView(APIView):
    permission_classes = [IsAuthenticated]

    def post(self, request):
        data = json.loads(request.body)
        try:
            url = config('CLS_HOST') + '/export/tham_quyen_hinh_thuc'
            headers = {
                'Content-Type': 'application/json'
            }

            response = requests.request("POST", url, headers=headers, data=json.dumps({"data":data}), timeout=REQUEST_TIME_OUT)
            if response.status_code == 200:
                # Forward the Excel file response
                excel_file = response.content
                response_headers = response.headers

                # Create a new HTTP response with the Excel file
                django_response = HttpResponse(
                    excel_file,
                    content_type=response_headers.get('Content-Type',
                                                      'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet')
                )
                django_response['Content-Disposition'] = response_headers.get('Content-Disposition',
                                                                              'attachment; filename="export.xlsx"')

                return django_response
            else:
                return HttpResponse(f"Error: {response.status_code}", status=response.status_code)
        except:
            return Response({}, status=status.HTTP_500_INTERNAL_SERVER_ERROR)

class ExportTQNDKetQuaView(APIView):
    permission_classes = [IsAuthenticated]

    def post(self, request):
        data = json.loads(request.body)["data"]
        try:
            url = config('CLS_HOST') + '/export/tham_quyen_noi_dung/ket_qua'
            headers = {
                'Content-Type': 'application/json'
            }

            response = requests.request("POST", url, headers=headers, data=json.dumps({"data":data}), timeout=REQUEST_TIME_OUT)
            if response.status_code == 200:
                # Forward the Excel file response
                excel_file = response.content
                response_headers = response.headers

                # Create a new HTTP response with the Excel file
                django_response = HttpResponse(
                    excel_file,
                    content_type=response_headers.get('Content-Type',
                                                      'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet')
                )
                django_response['Content-Disposition'] = response_headers.get('Content-Disposition',
                                                                              'attachment; filename="export.xlsx"')

                return django_response
            else:
                return HttpResponse(f"Error: {response.status_code}", status=response.status_code)
        except:
            return Response({}, status=status.HTTP_500_INTERNAL_SERVER_ERROR)
        
class ExportTQNDChiTietChuDeView(APIView):
    permission_classes = [IsAuthenticated]

    def post(self, request):
        data = json.loads(request.body)
        try:
            url = config('CLS_HOST') + '/export/tham_quyen_noi_dung/chi_tiet_chu_de'
            headers = {
                'Content-Type': 'application/json'
            }

            response = requests.request("POST", url, headers=headers, data=json.dumps(data), timeout=REQUEST_TIME_OUT)
            if response.status_code == 200:
                # Forward the Excel file response
                excel_file = response.content
                response_headers = response.headers

                # Create a new HTTP response with the Excel file
                django_response = HttpResponse(
                    excel_file,
                    content_type=response_headers.get('Content-Type',
                                                      'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet')
                )
                django_response['Content-Disposition'] = response_headers.get('Content-Disposition',
                                                                              'attachment; filename="export.xlsx"')

                return django_response
            else:
                return HttpResponse(f"Error: {response.status_code}", status=response.status_code)
        except:
            return Response({}, status=status.HTTP_500_INTERNAL_SERVER_ERROR)


class ExportTQNDChiTietQuyDinhView(APIView):
    permission_classes = [IsAuthenticated]

    def post(self, request):
        data = json.loads(request.body)
        try:
            url = config('CLS_HOST') + '/export/tham_quyen_noi_dung/chi_tiet_quy_dinh'
            headers = {
                'Content-Type': 'application/json'
            }

            response = requests.request("POST", url, headers=headers, data=json.dumps(data), timeout=REQUEST_TIME_OUT)
            if response.status_code == 200:
                # Forward the Excel file response
                excel_file = response.content
                response_headers = response.headers

                # Create a new HTTP response with the Excel file
                django_response = HttpResponse(
                    excel_file,
                    content_type=response_headers.get('Content-Type',
                                                      'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet')
                )
                django_response['Content-Disposition'] = response_headers.get('Content-Disposition',
                                                                              'attachment; filename="export.xlsx"')

                return django_response
            else:
                return HttpResponse(f"Error: {response.status_code}", status=response.status_code)
        except:
            return Response({}, status=status.HTTP_500_INTERNAL_SERVER_ERROR)
