



from urllib.parse import urljoin
from django.core.mail import EmailMultiAlternatives
from django.conf import settings
from datetime import datetime
import secrets
import string


def generate_random_password(length=8):
    """
    Tạo mật khẩu ngẫu nhiên an toàn

    Args:
        length: Đ<PERSON> dài mật khẩu (mặc định 8 ký tự)

    Returns:
        str: M<PERSON>t khẩu ngẫu nhiên
    """
    # Định nghĩa các ký tự có thể sử dụng
    lowercase = string.ascii_lowercase
    uppercase = string.ascii_uppercase
    digits = string.digits
    special_chars = "!@#$%^&*"

    # Đảm bảo mật khẩu có ít nhất 1 ký tự từ mỗi loại
    password = [
        secrets.choice(lowercase),
        secrets.choice(uppercase),
        secrets.choice(digits),
        secrets.choice(special_chars)
    ]

    # Thêm các ký tự ngẫu nhiên cho đến khi đủ độ dài
    all_chars = lowercase + uppercase + digits + special_chars
    for _ in range(length - 4):
        password.append(secrets.choice(all_chars))

    # Trộn ngẫu nhiên thứ tự các ký tự
    secrets.SystemRandom().shuffle(password)

    return ''.join(password)


def send_password_change_notification(user, new_password=None, is_reset_by_admin=False):
    """
    Gửi email thông báo đổi mật khẩu thành công
    
    Args:
        user: User object
        new_password: Mật khẩu mới (chỉ khi admin reset)
        is_reset_by_admin: True nếu admin reset, False nếu user tự đổi
    """
    subject = "Bạn đã đổi mật khẩu thành công"
    from_email = settings.DEFAULT_FROM_EMAIL
    to_email = [user.email]
    
    # Lấy thời gian hiện tại
    current_time = datetime.now().strftime("%d/%m/%Y")
    
    # Nội dung text thuần
    if is_reset_by_admin:
        text_content = f"""
        Xin chào {user.fullname or user.email},
        
        Mật khẩu hệ thống CLS của bạn đã được thay đổi thành công vào lúc {current_time}.
        
        Mật khẩu mới: {new_password}
        
        Nếu bạn là người thực hiện thao tác này, không cần thực hiện thêm hành động nào.
        
        Nếu bạn không thực hiện việc đổi mật khẩu, vui lòng:
        - Đổi lại mật khẩu ngay lập tức để đảm bảo an toàn.
        - Liên hệ với bộ phận hỗ trợ qua email: <EMAIL>
        
        Đây là email được gửi tự động, vui lòng không trả lời lại email này.
        
        Trân trọng,
        Đội ngũ CMC ATI
        """
    else:
        text_content = f"""
        Xin chào {user.fullname or user.email},
        
        Mật khẩu hệ thống CLS của bạn đã được thay đổi thành công vào lúc {current_time}.
        
        Nếu bạn là người thực hiện thao tác này, không cần thực hiện thêm hành động nào.
        
        Nếu bạn không thực hiện việc đổi mật khẩu, vui lòng:
        - Đổi lại mật khẩu ngay lập tức để đảm bảo an toàn.
        - Liên hệ với bộ phận hỗ trợ qua email: <EMAIL>
        
        Đây là email được gửi tự động, vui lòng không trả lời lại email này.
        
        Trân trọng,
        Đội ngũ CMC ATI
        """
    
    # Template HTML theo mẫu thiết kế
    password_info = f"""
        <tr>
            <td style="padding: 10px 0;">
                <strong>Mật khẩu mới:</strong> {new_password}
            </td>
        </tr>
    """ if is_reset_by_admin else ""
    
    html_content = f"""
    <!DOCTYPE html>
    <html>
    <head>
        <meta charset="UTF-8">
        <meta name="viewport" content="width=device-width, initial-scale=1.0">
        <title>Thông báo đổi mật khẩu</title>
    </head>
    <body style="margin: 0; padding: 20px; font-family: Arial, sans-serif; background-color: #f5f5f5;">
        <div style="max-width: 600px; margin: 0 auto; background-color: #ffffff; border-radius: 8px; overflow: hidden; box-shadow: 0 2px 10px rgba(0,0,0,0.1);">
            
            <!-- Header với logo -->
            <div style="background-color: #ffffff; padding: 30px 40px 20px; text-align: left; border-bottom: 1px solid #e0e0e0;">
                <div style="display: flex; flex-direction: column; align-items: center; margin-bottom: 20px;">
                    <div style="margin-bottom: 10px;">
                        <img src="https://cmcati.vn/wp-content/uploads/2023/06/CMC-ATI-06-e1691983908611.png" alt="Logo" style="width: 300px;">
                    </div>
                    
                </div>
                <div style="margin-bottom: 20px;">
                        <div style="font-size: 16px; font-weight: bold; color: #333;">CMC ATI</div>
                    </div>
                <h2 style="margin: 0; color: #333; font-size: 24px; font-weight: normal;">Bạn đã đổi mật khẩu thành công</h2>
            </div>
            
            <!-- Nội dung chính -->
            <div style="padding: 30px 40px;">
                <p style="margin: 0 0 20px; color: #333; line-height: 1.6;">
                    <strong>Xin chào {user.fullname or user.email},</strong>
                </p>
                
                <p style="margin: 0 0 20px; color: #333; line-height: 1.6;">
                    Mật khẩu hệ thống CLS của bạn đã được thay đổi thành công vào lúc <strong>{current_time}</strong>.
                </p>
                
                <table style="width: 100%; margin: 20px 0;">
                    {password_info}
                </table>
                
                <p style="margin: 20px 0; color: #333; line-height: 1.6;">
                    Nếu bạn là người thực hiện thao tác này, không cần thực hiện thêm hành động nào.
                </p>
                
                <p style="margin: 20px 0; color: #333; line-height: 1.6;">
                    Nếu bạn không thực hiện việc đổi mật khẩu, vui lòng:
                </p>
                
                <ul style="margin: 10px 0 20px 20px; color: #333; line-height: 1.6;">
                    <li>Đổi lại mật khẩu ngay lập tức để đảm bảo an toàn.</li>
                    <li>Liên hệ với bộ phận hỗ trợ qua email: <a href="mailto:<EMAIL>" style="color: #4A90E2; text-decoration: none;"><EMAIL></a></li>
                </ul>
                
                <p style="margin: 30px 0 20px; color: #666; font-size: 14px; line-height: 1.6;">
                    Đây là email được gửi tự động, vui lòng không trả lời lại email này.
                </p>
                
                <p style="margin: 20px 0 0; color: #333; line-height: 1.6;">
                    <strong>Trân trọng,</strong><br>
                    <strong>Đội ngũ CMC ATI</strong>
                </p>
            </div>
            
            <!-- Footer -->
            <div style="background-color: #f8f9fa; padding: 20px 40px; text-align: center; border-top: 1px solid #e0e0e0;">
                <div style="font-size: 24px; font-weight: bold; color: #ccc; margin-bottom: 10px;">CLS</div>
                <div style="font-size: 12px; color: #999;">© 2025 ATI All rights reserved</div>
            </div>
            
        </div>
    </body>
    </html>
    """
    
    # Tạo và gửi email
    msg = EmailMultiAlternatives(subject, text_content, from_email, to_email)
    msg.attach_alternative(html_content, "text/html")
    
    try:
        msg.send()
        return True
    except Exception as e:
        print(f"Lỗi gửi email: {str(e)}")
        return False


def send_account_creation_notification(user, password, frontend_url="https://cls.cmcati.vn"):
    """
    Gửi email thông báo tạo tài khoản thành công

    Args:
        user: User object
        password: Mật khẩu của tài khoản mới
        frontend_url: URL frontend để đăng nhập
    """
    subject = "Tài khoản của bạn đã được tạo thành công!"
    from_email = settings.DEFAULT_FROM_EMAIL
    to_email = [user.email]
    frontend_url = urljoin(frontend_url, "cls")
    # Nội dung text thuần
    text_content = f"""
    Xin chào {user.fullname or user.email},

    Tài khoản CLS của bạn đã được tạo, giờ bạn có thể đăng nhập và sử dụng CLS.

    Email: {user.email}
    Mật khẩu: {password}

    Bạn có thể truy cập tại đường dẫn dưới đây {frontend_url}/cls

    Đây là email được gửi tự động, vui lòng không trả lời lại email này.

    Vui lòng không cung cấp tài khoản cho người lạ. Có thắc mắc gì xin liên hệ <EMAIL>

    Trân trọng,
    Đội ngũ CMC ATI
    """

    # Template HTML theo mẫu thiết kế
    html_content = f"""
    <!DOCTYPE html>
    <html>
    <head>
        <meta charset="UTF-8">
        <meta name="viewport" content="width=device-width, initial-scale=1.0">
        <title>Tài khoản đã được tạo thành công</title>
    </head>
    <body style="margin: 0; padding: 20px; font-family: Arial, sans-serif; background-color: #f5f5f5;">
        <div style="max-width: 600px; margin: 0 auto; background-color: #ffffff; border-radius: 8px; overflow: hidden; box-shadow: 0 2px 10px rgba(0,0,0,0.1); border: 2px dashed #4A90E2;">

            <!-- Header với logo -->
            <div style="background-color: #ffffff; padding: 30px 40px 20px; text-align: left; border-bottom: 1px solid #e0e0e0;">
                <div style="display: flex; flex-direction: column; align-items: center; margin-bottom: 20px;">
                    <div style="margin-bottom: 10px;">
                        <img src="https://cmcati.vn/wp-content/uploads/2023/06/CMC-ATI-06-e1691983908611.png" alt="Logo" style="width: 300px;">
                    </div>

                </div>
                <div style="margin-bottom: 20px;">
                    <div style="font-size: 16px; font-weight: bold; color: #333;">CMC ATI</div>
                </div>
                <h2 style="margin: 0; color: #333; font-size: 24px; font-weight: normal;">Tài khoản của bạn đã được tạo thành công!</h2>
            </div>

            <!-- Nội dung chính -->
            <div style="padding: 30px 40px;">
                <p style="margin: 0 0 20px; color: #333; line-height: 1.6;">
                    <strong>Xin chào {user.fullname or user.email},</strong>
                </p>

                <p style="margin: 0 0 20px; color: #333; line-height: 1.6;">
                    Tài khoản CLS của bạn đã được tạo, giờ bạn có thể đăng nhập và sử dụng CLS.
                </p>

                <table style="width: 100%; margin: 20px 0; border-collapse: collapse;">
                    <tr>
                        <td style="padding: 10px 0; border-bottom: 1px solid #eee;">
                            <strong>Email:</strong> <span style="color: #4A90E2;">{user.email}</span>
                        </td>
                    </tr>
                    <tr>
                        <td style="padding: 10px 0; border-bottom: 1px solid #eee;">
                            <strong>Mật khẩu:</strong> <span style="color: #e74c3c; font-weight: bold;">{password}</span>
                        </td>
                    </tr>
                </table>

                <p style="margin: 20px 0; color: #333; line-height: 1.6;">
                    Bạn có thể truy cập tại đường dẫn dưới đây <a href="{frontend_url}" style="color: #4A90E2; text-decoration: none;">{frontend_url}</a>
                </p>

                <p style="margin: 30px 0 20px; color: #666; font-size: 14px; line-height: 1.6;">
                    Đây là email được gửi tự động, vui lòng không trả lời lại email này.
                </p>

                <p style="margin: 20px 0; color: #333; line-height: 1.6;">
                    Vui lòng không cung cấp tài khoản cho người lạ. Có thắc mắc gì xin liên hệ <a href="mailto:<EMAIL>" style="color: #4A90E2; text-decoration: none;"><EMAIL></a>
                </p>

                <p style="margin: 20px 0 0; color: #333; line-height: 1.6;">
                    <strong>Trân trọng,</strong><br>
                    <strong>Đội ngũ CMC ATI</strong>
                </p>
            </div>

            <!-- Footer -->
            <div style="background-color: #f8f9fa; padding: 20px 40px; text-align: center; border-top: 1px solid #e0e0e0;">
                <div style="font-size: 24px; font-weight: bold; color: #ccc; margin-bottom: 10px;">CLS</div>
                <div style="font-size: 12px; color: #999;">© 2025 ATI All rights reserved</div>
            </div>

        </div>
    </body>
    </html>
    """

    # Tạo và gửi email
    msg = EmailMultiAlternatives(subject, text_content, from_email, to_email)
    msg.attach_alternative(html_content, "text/html")

    try:
        msg.send()
        return True
    except Exception as e:
        print(f"Lỗi gửi email: {str(e)}")
        return False
