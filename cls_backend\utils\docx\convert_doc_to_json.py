import re
import subprocess
import xml.etree.ElementTree as ET
from typing import List, Dict, NoReturn, Optional, Any, Union

import cls_backend.utils.docx.config as cfg
from cls_backend.utils.docx.convert_docx_to_xml import docx_to_xml
from cls_backend.utils.docx.flatten import flatten_json
from cls_backend.utils.docx.pdfscan2docx import *

TABLE_T = List[List[List[Dict[str, Any]]]]
from decouple import config
import logging


logger = logging.getLogger("cls")

class DocToJson:
    def __init__(self):
        self.relations = None
        self.file_name = None
        self.file_id = None
        self.index_tracking = {i: 0 for i in range(cfg.max_level)}
        self.count_text = 0

    def file_to_json(
            self,
            file_path: str,
            file_id: Optional[str] = None,
    ) -> Union[NoReturn, Dict[str, Dict]]:
        if self.is_pdf(file_path):
            docx_path = convert_scan2_docx(file_path)
        elif self.is_docx(file_path):
            docx_path = file_path
        elif self.is_doc(file_path):
            docx_path = self.doc_to_docx(file_path)
        else:
            return {"document": []}
        file_name = os.path.basename(file_path)
        document = self.docx_to_json(docx_path=docx_path, file_name=file_name, file_id=file_id)
        document = flatten_json(document)

        return document

    @staticmethod
    def is_pdf(path: str) -> bool:
        return path.endswith(".pdf")

    @staticmethod
    def is_doc(path: str) -> bool:
        return path.lower().endswith(".doc")

    @staticmethod
    def is_docx(path: str) -> bool:
        return path.endswith(".docx")

    @staticmethod
    def is_txt(path: str) -> bool:
        return path.endswith(".txt")

    def docx_to_json(
            self,
            docx_path: str,
            file_name: Optional[str] = None,
            file_id: Optional[str] = None
    ) -> Dict[str, List[Dict[str, Any]]]:
        output_dir = os.path.dirname(docx_path)
        logger.debug(docx_path)
        document_path, relations_path, _ = docx_to_xml(docx_path, output_dir)
        if document_path is None or relations_path is None:
            return {"document": []}
        else:
            paras = ET.parse(document_path).getroot().find(self.expand("w:body"))

            self.relations = ET.parse(relations_path).getroot()
            self.file_name = file_name
            self.file_id = file_id
            document = []
            for idx, para in enumerate(paras):
                content = None
                if self.is_table(para):
                    tmp_content = []
                    content = self.get_table_content(para)
                    # HOT FIX: nếu có nhiều text trong bảng -> bảng thật -> skip
                    if len(list(content["paraContent"][0].values())[0]) > 10: continue
                    # Nếu vị trí bảng ở đầu file -> nhận diện sai -> append hết text vào
                    elif idx < 10:
                        paraContent = content.get("paraContent", [])
                        if paraContent: paraContent = paraContent[0].get('table', [])
                        if paraContent:
                            for row in paraContent:
                                for col in row:
                                    for item in col:
                                        text = item.get("paraContent", [])
                                        if text:
                                            tmp_content.append(item)
                        content = tmp_content
                elif self.is_paragraph(para):
                    content: Union[str, Dict[str, Any]] = self.get_para_object(para)
                    if isinstance(content, str):
                        for i, c in enumerate(document[-1][cfg.paraContent]):
                            if cfg.image in c:
                                document[-1][cfg.paraContent][i][cfg.image][cfg.imageDescription] = content

                if isinstance(content, dict) and content[cfg.paraContent]:
                    # print(content["paraContent"][0])
                    document.append(content)
                elif isinstance(content, list):
                    document = document + content

            return {"document": document}

    def get_table_content(self, table) -> Dict[str, Any]:
        """Read table start with tag <w:tbl>"""
        rows: TABLE_T = []
        for row in table:
            cols = []
            for col in row:
                # Get grid span
                span = col.find(f"./{self.expand('w:tcPr')}")
                if span is not None:
                    span = span.find(f".//{self.expand('w:gridSpan')}")
                    if span is not None:
                        span = span.attrib.get(self.expand("w:val"))
                if span is not None and isinstance(span, str):
                    span = int(span)

                # Get text
                paras = col.findall(f'./{self.expand("w:p")}')
                if paras is not None:
                    para_objs: List[Dict[str, Any]] = []
                    for para in paras:
                        obj: Dict[str, Any] = self.get_para_object(para)
                        if obj is not None:
                            if span is not None and span > 1:
                                obj[cfg.span] = True
                            para_objs.append(obj)
                    if para_objs:
                        if span is not None and span > 1:
                            cols.extend([para_objs] * span)
                        else:
                            cols.append(para_objs)
            if cols:
                rows.append(cols)

        return {
            cfg.fileId: self.file_id,
            cfg.fileName: self.file_name,
            cfg.paraIndex: None,
            cfg.parentIndex: self.get_parent_index_from_para_level(None),
            cfg.paraLevel: None,
            cfg.paraContent: [{"table": rows}]
        }

    def get_para_object(self, para) -> Union[str, Dict[str, Any]]:
        """Read paragraph start with tag <w:p>"""
        para_contents = []

        for e in para:
            hyperlink = self.get_hyperlink(e)
            if hyperlink:
                para_contents.append({cfg.text: hyperlink})
                continue

            text = self.get_text(e)
            if text:
                if self.is_picture_caption(self.get_para_style(para)):
                    return text.get("value")
                para_contents.append({cfg.text: text})
                continue

            image = self.get_image(e)
            if image:
                image[cfg.textId] = self.count_text
                self.count_text += 1
                para_contents.append({cfg.image: image})

        c_para_contents = []  # concatenated_para_contents
        c_text = ""  # concatenated_text
        first_text = ""
        for e in para_contents:
            if cfg.text in e:
                text = e.get(cfg.text, {}).get("value", "")
                text = " " if not text else text
                c_text += text
            else:
                if c_text:
                    c_para_contents.append(
                        {cfg.text: {cfg.textId: self.count_text, cfg.textValue: " ".join(c_text.split())}})
                    c_text = ""
                    self.count_text += 1
                    if not first_text:
                        first_text = c_text
                c_para_contents.append(e)

        if c_text:
            c_text = " ".join(c_text.split())
            c_para_contents.append({cfg.text: {cfg.textId: self.count_text, cfg.textValue: c_text}})
            self.count_text += 1
            if not first_text:
                first_text = c_text

        para_level = self.get_para_level_from_para_text(first_text.lower().strip())
        para_index = self.get_para_index_from_para_level(para_level)
        parent_index = self.get_parent_index_from_para_level(para_level)

        return {
            cfg.fileId: self.file_id,
            cfg.fileName: self.file_name,
            cfg.paraIndex: para_index,
            cfg.parentIndex: parent_index,
            cfg.paraLevel: para_level,
            cfg.paraContent: c_para_contents
        }

    def get_text(self, para) -> Dict[str, str]:
        text = para.find(f'.//{self.expand("w:t")}')
        if text is not None:
            text = text.text
            text = text if text is not None else ""
            return {
                "value": text,
                # "properties": {
                #     "hyperlink": 0,
                #     "target": None
                # }
            }

    def get_hyperlink(self, para) -> Dict[str, str]:
        if self.is_hyperlink(para):
            texts = para.findall(f'.//{self.expand("w:t")}')
            text = ""
            for t in texts:
                text += t.text
            text = text if text is not None else ""
            # target_id = para.get(self.expand("r:id"))
            # target = self.get_target(target_id)
            return {
                "value": text,
                # "properties": {
                #     "hyperlink": 1,
                #     "target": target
                # }
            }

    def get_image(self, para) -> Dict[str, str]:
        descr = para.find(f'.//{self.expand("pic:pic")}/{self.expand("pic:nvPicPr")}/{self.expand("pic:cNvPr")}')
        descr = descr.get("description") if descr is not None else ""
        descr = descr if descr is not None else ""
        target_id = para.find(f'.//{self.expand("pic:pic")}/{self.expand("pic:blipFill")}/{self.expand("a:blip")}')
        target_id = target_id.get(self.expand("r:embed")) if target_id is not None else None
        target = self.get_target(target_id)
        if target_id:
            return {
                cfg.imageDescription: descr,
                cfg.imageTarget: target
            }

    def update_index_tracking(self, current_level: int):
        if current_level is not None:
            for k, v in self.index_tracking.items():
                if k == current_level:
                    self.index_tracking[k] = v + 1
                elif k > current_level:
                    self.index_tracking[k] = 0

    def get_target(self, target_id: str) -> str:
        for r in self.relations:
            if r.get("Id") == target_id:
                return r.get("Target")

    def get_para_style(self, para) -> str:
        para_style = para.find(f'.//{self.expand("w:pStyle")}')
        if para_style is not None:
            para_style = para_style.attrib.get(self.expand("w:val")).lower()
        return para_style

    def get_para_level_from_para_text(self, para_text: str) -> int:
        if isinstance(para_text, str):
            if self.is_heading(para_text):
                for e in cfg.case_sensitive_heading_patterns:
                    if re.match(e, para_text):
                        return cfg.case_sensitive_heading_patterns.get(e)
                para_text = para_text.lower().strip()
                for e in cfg.lowercase_heading_patterns:
                    if re.match(e, para_text):
                        return cfg.lowercase_heading_patterns.get(e)

    def get_para_index_from_para_level(self, para_level: int):
        if para_level is not None:
            self.update_index_tracking(para_level)
            return self.get_index_from_index_tracking(para_level)

    def get_parent_index_from_para_level(self, para_level: int):
        if para_level is None:
            return self.get_index_from_index_tracking(cfg.max_level - 1)
        elif para_level == 0:
            return None
        return self.get_index_from_index_tracking(para_level - 1)

    def get_index_from_index_tracking(self, end_at_level: int = None):
        if end_at_level is None:
            end_at_level = cfg.max_level - 1
        index = ".".join([str(e) for e in list(self.index_tracking.values())[:end_at_level + 1]])
        return self.pad_index_length(index)

    def pad_index_length(self, index: str):
        index = index.split(".")
        index_length = len(index)
        index.extend(["0"] * (cfg.max_level - index_length))
        return ".".join(index)

    def is_table(self, para) -> bool:
        return self.shorten(para.tag) in self.table_tags()

    def is_paragraph(self, para) -> bool:
        return self.shorten(para.tag) in self.para_tags()

    def is_picture_caption(self, para_style: str) -> bool:
        return isinstance(para_style, str) and para_style in self.picture_caption_styles()

    def is_hyperlink(self, para):
        return self.shorten(para.tag) in self.hyperlink_tags()

    def is_heading(self, para_text: str) -> bool:
        if isinstance(para_text, str):
            para_text = para_text.strip()
            return any(re.match(e, para_text.lower()) for e in cfg.lowercase_heading_patterns) or any(
                re.match(e, para_text) for e in cfg.case_sensitive_heading_patterns)
        return False

    @staticmethod
    def table_tags():
        return ["tbl"]

    @staticmethod
    def para_tags():
        return ["p"]

    @staticmethod
    def hyperlink_tags():
        return ["hyperlink"]

    @staticmethod
    def picture_caption_styles():
        return ["picture", "picturedescription"]

    def expand(self, tag: str) -> str:
        _type, tag = tag.split(":")[0], tag.split(":")[1]
        tag = self.tag_mappings().get(_type, "") + tag
        return tag

    @staticmethod
    def shorten(tag: str) -> str:
        tag = tag.split("}")[-1]
        return tag

    @staticmethod
    def tag_mappings() -> Dict[str, str]:
        return {
            "mc": "{http://schemas.openxmlformats.org/markup-compatibility/2006}",
            "o": "{urn:schemas-microsoft-com:office:office}",
            "r": "{http://schemas.openxmlformats.org/officeDocument/2006/relationships}",
            "m": "{http://schemas.openxmlformats.org/officeDocument/2006/math}",
            "v": "{urn:schemas-microsoft-com:vml}",
            "wp": "{http://schemas.openxmlformats.org/drawingml/2006/wordprocessingDrawing}",
            "w10": "{urn:schemas-microsoft-com:office:word}",
            "w": "{http://schemas.openxmlformats.org/wordprocessingml/2006/main}",
            "wne": "{http://schemas.microsoft.com/office/word/2006/wordml}",
            "sl": "{http://schemas.openxmlformats.org/schemaLibrary/2006/main}",
            "a": "{http://schemas.openxmlformats.org/drawingml/2006/main}",
            "pic": "{http://schemas.openxmlformats.org/drawingml/2006/picture}",
            "c": "{http://schemas.openxmlformats.org/drawingml/2006/chart}",
            "lc": "{http://schemas.openxmlformats.org/drawingml/2006/lockedCanvas}",
            "dgm": "{http://schemas.openxmlformats.org/drawingml/2006/diagram}",
            "wps": "{http://schemas.microsoft.com/office/word/2010/wordprocessingShape}",
            "wpg": "{http://schemas.microsoft.com/office/word/2010/wordprocessingGroup}",
            "w14": "{http://schemas.microsoft.com/office/word/2010/wordml}",
            "w15": "{http://schemas.microsoft.com/office/word/2012/wordml}"
        }

    def doc_to_docx(self, doc_path: str) -> str:
        """
        Chuyển đổi file .doc sang .docx bằng LibreOffice (soffice).
        """
        if not os.path.exists(doc_path):
            raise FileNotFoundError(f"Không tìm thấy file: {doc_path}")

        output_dir = os.path.dirname(doc_path)
        # soffice_path = r"C:\Program Files\LibreOffice\program\soffice.exe"
        try:
            subprocess.run(
                [
                    # soffice_path,
                    "soffice",
                    "--headless",
                    "--convert-to", "docx",
                    "--outdir", output_dir,
                    doc_path
                ],
                check=True,
                stdout=subprocess.PIPE,
                stderr=subprocess.PIPE
            )
        except subprocess.CalledProcessError as e:
            raise RuntimeError(f"Lỗi khi chuyển đổi .doc -> .docx: {e.stderr.decode()}")

        base = os.path.splitext(doc_path)[0]
        docx_path = f"{base}.docx"

        if not os.path.exists(docx_path):
            raise FileNotFoundError(f"Không tìm thấy file .docx sau khi chuyển đổi: {docx_path}")

        return docx_path


doc_instance = DocToJson()
