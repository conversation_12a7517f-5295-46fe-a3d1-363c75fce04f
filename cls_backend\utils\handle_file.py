import os

from django.core.files.uploadedfile import InMemoryUploadedFile
from requests_toolbelt.multipart.encoder import MultipartEncoder

import redis
import json
from typing import Dict, Any
from django.conf import settings
import logging


logger = logging.getLogger("cls")

def create_multipart_data_from_file(file):
    file_name, extension = os.path.splitext(file.name)
    if type(file) is InMemoryUploadedFile:
        file_name = file.name
        file = file.file
    else:
        file_name = file.name.replace('\\', '/').split('/')[-1]
        file = file.read()
    extension = extension.lower()
    if extension.count('pdf') > 0:
        # PDF
        file = (file_name, file, 'application/pdf')
    elif extension.count('docx') > 0:
        # DOCX
        file = (file_name, file, 'application/vnd.openxmlformats-officedocument.wordprocessingml.document')
    elif extension.count('doc') > 0:
        # DOC
        file = (file_name, file, 'application/msword')
    else:
        raise ValueError(f"Không hỗ trợ định dạng file {extension}. <PERSON>ị<PERSON> dạng hỗ trợ: pdf, docx.")

    return MultipartEncoder(
        fields={
            'file': file
        }
    )

class RedisUtil:
    _instance_store = {}
    _redis_client = None

    @classmethod
    def get_instance(cls, type_redis: str): # cls means class not that CLS!!!
        if type_redis in cls._instance_store:
            return cls._instance_store[type_redis]
        
        instance = cls(type_redis)
        cls._instance_store[type_redis] = instance
        return instance

    def __init__(self, type_redis: str):
        # logger.debug([settings.REDIS_HOST, settings.REDIS_PORT, settings.REDIS_PASSWORD, settings.CELERY_REDIS_DB])
        # if self._redis_client is None:
        if type_redis == 'celery':
            self._redis_client = redis.Redis(
                host=settings.REDIS_HOST,
                port=settings.REDIS_PORT,
                db=settings.CELERY_REDIS_DB,
                password=settings.REDIS_PASSWORD,
                decode_responses=True
            )
        elif type_redis == 'ocr':
            self._redis_client = redis.Redis(
                host=settings.OCR_REDIS_HOST,
                port=settings.OCR_REDIS_PORT,
                db=settings.OCR_REDIS_DB,
                password=settings.OCR_REDIS_PASSWORD,
                decode_responses=True
            )
        
    
    @classmethod
    def get_job_status(cls, job_id: str, type_redis: str) -> Dict[str, Any]:
        """
        Get job status from Redis by job_id
        
        Args:
            job_id (str): The unique identifier for the job
            
        Returns:
            Dict with status and message
            
        Raises:
            Exception: If there's an error retrieving or parsing the status
        """
        try:
            instance = cls.get_instance(type_redis)
            result = instance._redis_client.get(job_id)
            
            if result is None:
                return {
                    "status": 0,
                    "message": "Job is still processing"
                }
            
            try:
                status_data = json.loads(result)
                return status_data
            except json.JSONDecodeError:
                logger.error(f"Failed to parse Redis data for job_id: {job_id}")
                return {
                    "status": 999,
                    "message": "Invalid job status format"
                }
                
        except redis.RedisError as e:
            logger.error(f"Redis error for job_id {job_id}: {str(e)}")
            logger.exception(e)
            return {
                "status": 998,
                "message": f"Redis connection error: {str(e)}"
            }
        except Exception as e:
            logger.error(f"Unexpected error for job_id {job_id}: {str(e)}")
            logger.exception(e)
            return {
                "status": 997,
                "message": f"Unexpected error: {str(e)}"
            }

    @classmethod
    def set_job_status(cls, job_id: str, status: Dict[str, Any], expiry: int = 3600) -> bool:
        """
        Set job status in Redis
        
        Args:
            job_id (str): The unique identifier for the job
            status (Dict): Status dictionary to store
            expiry (int): Time in seconds before the key expires (default 1 hour)
            
        Returns:
            bool: True if successful, False otherwise
        """
        try:
            instance = cls.get_instance(type_redis)
            status_json = json.dumps(status)
            instance._redis_client.set(job_id, status_json, ex=expiry)
            return True
        except Exception as e:
            logger.error(f"Failed to set job status for {job_id}: {str(e)}")
            logger.exception(e)
            return False

    @classmethod
    def delete_job_status(cls, job_id: str, type_redis: str) -> bool:
        """
        Delete job status from Redis
        
        Args:
            job_id (str): The unique identifier for the job
            
        Returns:
            bool: True if successful, False otherwise
        """
        try:
            instance = cls.get_instance(type_redis)
            instance._redis_client.delete(job_id)
            return True
        except Exception as e:
            logger.error(f"Failed to delete job status for {job_id}: {str(e)}")
            logger.exception(e)
            return False

    @classmethod
    def check_job_status_with_timeout(cls, job_id: str, type_redis: str, time_limit: int = 300, check_interval: int = 2) -> Dict[str, Any]:
        """
        Check job status with timeout
        
        Args:
            job_id (str): The unique identifier for the job
            time_limit (int): Maximum time to wait in seconds (default 5 minutes)
            check_interval (int): Time between checks in seconds (default 2 seconds)
            
        Returns:
            Dict with job status and message
            
        Raises:
            Exception: If timeout occurs or job fails
        """
        import time
        
        start = time.time()
        while (time.time() - start) < time_limit:
            try:
                job_status = cls.get_job_status(job_id, type_redis)
                # logger.debug(job_status)
                if job_status["status"] == 1:
                    return 1
                elif job_status["status"] in [-1, 996, 997, 998, 999]:
                    err_message = job_status['message']
                    logger.error(err_message)
                    raise Exception(err_message)
                    
            except Exception as err:
                logger.error(f"Error checking job status: {str(err)}")
                logger.exception(err)
                raise err
            
            time.sleep(check_interval)
        
        err_message = f"Process timeout > {time_limit} seconds"
        logger.error(err_message)
        raise Exception(err_message)