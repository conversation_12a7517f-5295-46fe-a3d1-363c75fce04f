from django.http import HttpResponse
from .models import RequestLog
from django.contrib.auth.models import AnonymousUser
import json
from functools import wraps

def log_requests(feature_name, save_response=True, skip_content_types=None):
    if skip_content_types is None:
        skip_content_types = ['image', 'streaming']

    def decorator(view_func):
        @wraps(view_func)
        def wrapper(self, request, *args, **kwargs):
            # Skip logging for specified paths
            response = view_func(self, request, *args, **kwargs)
            
            # Skip if response shouldn't be saved
            content_type = response.get('Content-Type', '')
            if any(ct in content_type for ct in skip_content_types) or not save_response:
                response_content = None
            else:
                try:
                    response_content = response.data
                except UnicodeDecodeError:
                    response_content = "<binary data>"
            
            # Prepare parameters (GET/POST data)
            params = {}
            if request.method == "GET":
                params['GET'] = dict(request.GET)
            if request.method == "POST":
                params['POST'] = dict(request.POST)
            
            # Filter sensitive keys
            SENSITIVE_KEYS = ['password', 'token', 'secret']
            for key in SENSITIVE_KEYS:
                if key in params.get('POST', {}):
                    params['POST'][key] = '*****'
            
            # Prepare metadata (headers)
            meta = {
                'REMOTE_ADDR': request.META.get('REMOTE_ADDR'),
                'HTTP_USER_AGENT': request.META.get('HTTP_USER_AGENT'),
                'HTTP_REFERER': request.META.get('HTTP_REFERER'),
            }
            
            # Save to database
            RequestLog.objects.create(
                user=request.user if not isinstance(request.user, AnonymousUser) else None,
                feature_name=feature_name,
                method=request.method,
                status_code=response.status_code,
                response=response_content,
                parameters=params if params else None,
                meta=meta
            )
            
            return response
        return wrapper
    return decorator