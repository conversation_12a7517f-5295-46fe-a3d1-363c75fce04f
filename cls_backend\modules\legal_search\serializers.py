from rest_framework import serializers
import os

from cls_backend.models import HistorySearchResult, LegalSearchHistory, Document


class DocumentSerializer(serializers.ModelSerializer):

    class Meta:
        model = Document
        fields = [
            'id', 'loai_van_ban', 'co_quan_ban_hanh', 'ngay_ban_hanh',
            'ngay_co_hieu_luc', 'ngay_dang_cong_bao', 'ngay_het_hieu_luc',
            'so_hieu', 'tinh_trang_hieu_luc', 'title', 'dia_danh',
            'trich_yeu', 'toan_van', 'nguoi_ky'
        ]

class LegalSearchHistorySerializer(serializers.ModelSerializer):
    class Meta:
        model = LegalSearchHistory
        fields = ['id', 'query', 'result_count', 'response_time', 'domain_list', 'requested_by', 'created_time']
class HistorySearchResultSerializer(serializers.ModelSerializer):
    class Meta:
        model = HistorySearchResult
        fields = ['id', 'user_id', 'data', 'params', 'search_time', 'type']
        extra_kwargs = {'user': {'required': False}}  # Không yêu cầu user trong request