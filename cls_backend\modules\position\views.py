from rest_framework import viewsets
from rest_framework.exceptions import PermissionDenied
from cls_backend.models import Position
from cls_backend.modules.position.permissions import IsSuperAdminOrOrganizationAdmin
from organization.models import OrganizationMember
from organization.constants import *
from .serializers import PositionSerializer

class PositionViewSet(viewsets.ModelViewSet):
    permission_classes = [IsSuperAdminOrOrganizationAdmin]
    queryset = Position.objects.all()
    serializer_class = PositionSerializer
    
    def perform_create(self, serializer):
        user = self.request.user
        organization = serializer.validated_data.get("organization")

        if user.is_staff:
            serializer.save()
            return

        is_admin = OrganizationMember.objects.filter(
            user=user,
            organization=organization,
            role=ADMIN,
            is_active=True
        ).exists()

        if not is_admin:
            raise PermissionDenied("Bạn không có quyền tạo position trong tổ chức này.")

        serializer.save()
        
    def get_queryset(self):
        user = self.request.user
        organization_id = self.request.query_params.get('organization')
        queryset = Position.objects.all()
        if user.is_staff:
            if organization_id:
                queryset = queryset.filter(organization_id=organization_id)
            return queryset

        # Lọc theo organization user là admin nếu không phải staff
        admin_orgs = OrganizationMember.objects.filter(
            user=user,
            role=ADMIN,
            is_active=True
        ).values_list('organization_id', flat=True)

        if organization_id:
            if str(organization_id) not in [str(org_id) for org_id in admin_orgs]:
                return Position.objects.none()
            return Position.objects.filter(organization_id=organization_id)

        # Nếu không truyền organization, trả về tất cả position thuộc các org user là admin
        if not admin_orgs:
            return Position.objects.none()
        return Position.objects.filter(organization_id__in=admin_orgs)