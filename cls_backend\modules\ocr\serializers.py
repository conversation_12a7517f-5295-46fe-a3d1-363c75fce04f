from rest_framework import serializers

from cls_backend.models import Document, DocumentRelated, Note, ThamQuyenNoiDung, WorkSpace, LawClause
from cls_backend.utils.file_validator import FileValidator

UPLOAD_MAX_SIZE = 200 * 1024 * 1024  # 200 MB in bytes
validate_file = FileValidator(max_size=UPLOAD_MAX_SIZE,
                              content_types=[
                                  'application/pdf',  # pdf
                                  'application/vnd.openxmlformats-officedocument.wordprocessingml.document',  # docx
                                  'application/msword',  # doc
                              ])


class DocumentSerializer(serializers.ModelSerializer):
    workspace_id = serializers.PrimaryKeyRelatedField(
        source="workspace", queryset=WorkSpace.objects.all()
    )


    origin = serializers.FileField(required=False, allow_null=True)
    ngay_ban_hanh = serializers.DateTimeField(required=False, allow_null=True)
    ngay_co_hieu_luc = serializers.DateTimeField(required=False, allow_null=True)
    ngay_dang_cong_bao = serializers.DateTimeField(required=False, allow_null=True)
    ngay_het_hieu_luc = serializers.DateTimeField(required=False, allow_null=True)
    ngay_het_hieu_luc_mot_phan = serializers.DateTimeField(required=False, allow_null=True)

    convert = serializers.FileField(read_only=True)
    # status = serializers.CharField(source='get_status_display', read_only=True)
    status_display = serializers.CharField(source='get_status_display', read_only=True)
    created_at = serializers.DateTimeField(format="%Y-%m-%d %H:%M:%S", read_only=True)
    name = serializers.CharField(read_only=True)
    is_convert = serializers.BooleanField(read_only=True)
    # percentage = serializers.IntegerField(source='percentage', read_only=True)
    types = serializers.ChoiceField(choices=Document.TYPES, required=False)

    class Meta:
        model = Document
        fields = ['id', 'origin', 'convert', 'status', 'status_display', 'created_at', 'name', 'is_convert', 'percentage', 'workspace_id', 'types',
                  'ID','title','dia_danh','ngay_ban_hanh','ngay_co_hieu_luc','ngay_dang_cong_bao','ngay_het_hieu_luc','ngay_het_hieu_luc_mot_phan','so_hieu','toan_van','trich_yeu','tinh_trang_hieu_luc','co_quan_ban_hanh','nguoi_ky','loai_van_ban','es_id']
        read_only_fields = ['id', 'convert', 'status', 'created_at', 'name', 'is_convert', 'percentage', 'types']


class DocumentLessInfoSerializer(serializers.ModelSerializer):
    workspace_id = serializers.PrimaryKeyRelatedField(
        source="workspace", queryset=WorkSpace.objects.all()
    )

    status_display = serializers.CharField(source='get_status_display', read_only=True)
    # percentage = serializers.IntegerField(source='convert_percentage', read_only=True)

    class Meta:
        model = Document
        fields = ['id', 'origin', 'convert', 'status', 'status_display', 'created_at', 'name', 'is_convert', 'percentage', 'workspace_id', 'types', 'tinh_trang_hieu_luc']
        read_only_fields = fields

class LawClauseSerializer(serializers.ModelSerializer):
    document_id = serializers.PrimaryKeyRelatedField(
        source='document', 
        queryset=Document.objects.all(),
        allow_null=True
    )
    # related_clauses = serializers.PrimaryKeyRelatedField(many=True, read_only=True)
    status_display = serializers.CharField(source='get_status_display', read_only=True)

    class Meta:
        model = LawClause
        fields = ['id', 'document_id', 'related_clauses', 'clause_id', 'title', 
                 'position', 'content', 'show_content', 'doc_id', 'is_raw',
                 'created_at', 'updated_at', 'status', 'status_display',
                 'result', 'reason', 'type', 'different_status', 'ly_do', 'ket_luan', 'giai_phap'
                 ]
        read_only_fields = ['id', 'created_at', 'updated_at']

class LawClauseLessInfoSerializer(serializers.ModelSerializer):
    # related_clauses = serializers.PrimaryKeyRelatedField(many=True, read_only=True)
    status_display = serializers.CharField(source='get_status_display', read_only=True)

    class Meta:
        model = LawClause
        fields = [
            'id', 'clause_id', 'title', 
            'position', 'doc_id', 
            'is_raw',
            'status', 
            'status_display',
        ]
        read_only_fields = ['id', 'created_at', 'updated_at']

class RelatedLawClauseSerializer(serializers.ModelSerializer):
    status_display = serializers.CharField(source='get_status_display', read_only=True)
    reason = serializers.SerializerMethodField()
    class Meta:
        model = LawClause
        fields = ['id', 'clause_id', 'title', 
                 'position', 'content', 'show_content', 'doc_id', 'is_raw',
                 'created_at', 'updated_at', 'status', 'status_display',
                 'result', 'reason', 'type', 'different_status', 'ly_do', 'ket_luan', 'giai_phap'
                 ]
        read_only_fields = ['id', 'created_at', 'updated_at']

    def get_reason(self, obj):
        if None in [obj.ly_do, obj.ket_luan, obj.giai_phap]:
            return None
        return obj.reason
        
class DocumentRelatedSerializer(serializers.ModelSerializer):
    document_id = serializers.PrimaryKeyRelatedField(source='document', queryset=Document.objects.all())

    class Meta:
        model = DocumentRelated
        fields = [
            'id', 'document_id', 'relation_type', 'score', 'url', 
            'pham_vi', 'so_hieu', 'vbpl_id', 'nguoi_ky', 'ten_van_ban','loai_van_ban',
            'ngay_ap_dung', 'ngay_ban_hanh','co_quan_ban_hanh', 'ngay_co_hieu_luc', 'tinh_trang_hieu_luc',
            'trich_yeu', 'ngay_co_hieu_luc', 'don_vi', 'title', 'toan_van','noi_dung'
        ]
        read_only_fields = [
            'id'
        ]

class DocumentRelatedLessInfoSerializer(serializers.ModelSerializer):
    class Meta:
        model = DocumentRelated
        fields = [
            'id', 'relation_type', 'score', 'url', 
            'pham_vi', 'so_hieu', 'vbpl_id', 'nguoi_ky', 'ten_van_ban','loai_van_ban',
            'ngay_ap_dung', 'ngay_ban_hanh','co_quan_ban_hanh', 'ngay_co_hieu_luc', 'tinh_trang_hieu_luc',
            'trich_yeu', 'ngay_co_hieu_luc', 'don_vi', 'title',
        ]
        read_only_fields = fields

class TQNDSerializer(serializers.ModelSerializer):
    related_clauses = LawClauseSerializer
    class Meta:
        model = ThamQuyenNoiDung
        fields = [
            'id', 'co_quan_co_tham_quyen', 'van_de_ban_hanh','ket_qua', 'ly_do', 'related_clauses'
        ]
        read_only_fields = fields
