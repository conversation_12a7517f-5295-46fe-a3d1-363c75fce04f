from django.urls import include, re_path, path
from rest_framework import routers
from organization.organization_member.views import OrganizationMemberViewSet
from organization.views import OrganizationViewSet

# Routers provide a way of automatically determining the URL conf.
router = routers.DefaultRouter()
# router.register("chat", LLMAPIView)
# Wire up our API using automatic URL routing.
# Additionally, we include login URLs for the browsable API.
router.register('organization', OrganizationViewSet, basename='organization')
router.register('organization-member', OrganizationMemberViewSet, basename='organization_member')

urlpatterns = [
    path('', include(router.urls)),
]