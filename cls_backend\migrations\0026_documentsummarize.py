# Generated by Django 4.2 on 2025-07-04 08:37

from django.db import migrations, models
import django.db.models.deletion


class Migration(migrations.Migration):

    dependencies = [
        ('cls_backend', '0025_alter_document_tinh_trang_hieu_luc'),
    ]

    operations = [
        migrations.CreateModel(
            name='DocumentSummarize',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
                ('deleted_at', models.DateTimeField(blank=True, null=True)),
                ('summarize', models.TextField(blank=True, null=True)),
                ('document', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.CASCADE, to='cls_backend.document')),
            ],
            options={
                'abstract': False,
            },
        ),
    ]
