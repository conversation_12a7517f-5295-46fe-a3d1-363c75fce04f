from django.shortcuts import get_object_or_404
from cls_backend.models import Package, <PERSON><PERSON><PERSON>, User, HistorySearchResult
from chatbot.models import ChatMessage
from cls_backend.modules.auth.backends import JWTAuthentication
from cls_backend.modules.package.serializers import PackageSerializer
from rest_framework.permissions import <PERSON>ow<PERSON><PERSON>, IsAuthenticated, IsAdminUser
from rest_framework.response import Response
from rest_framework.viewsets import GenericViewSet
from rest_framework.generics import UpdateAPIView
from rest_framework import status
from rest_framework.views import APIView
from rest_framework.permissions import DjangoModelPermissionsOrAnonReadOnly
from django.utils import timezone
from datetime import timedelta
from chatbot.constants import LLM_MESSAGE

class UpdatePackageAPIView(GenericViewSet, UpdateAPIView):
    queryset = Package.objects.all()
    serializer_class = PackageSerializer
    permission_classes = [IsAdminUser]

    def update(self, request, *args, **kwargs):
        try:
            package = self.get_object()
        except Package.DoesNotExist:
            return Response({"error": "Package không tồn tại."}, status=status.HTTP_404_NOT_FOUND)

        serializer = self.serializer_class(package, data=request.data, partial=True)
        if serializer.is_valid():
            serializer.save()
            return Response(serializer.data, status=status.HTTP_200_OK)
        return Response({"error": serializer.errors}, status=status.HTTP_400_BAD_REQUEST)

class GetPackageByUserIdAPIView(APIView):
    permission_classes = [IsAuthenticated]
    def get(self, request, user_id):
            user = get_object_or_404(User, id=user_id)
            print(request.headers)  # Debug headers
            print(request.user)  # Xem user đã authenticate chưa
            # Kiểm tra Quota có tồn tại không
            quota = Quota.objects.filter(user=user).select_related("package").first()
            if not quota:
                return Response({"error": "User này chưa có package."}, status=status.HTTP_404_NOT_FOUND)

            # Trả về dữ liệu package
            serializer = PackageSerializer(quota.package)
            return Response(serializer.data, status=status.HTTP_200_OK)

class UpdatePackageByIdAPIView(APIView):
    permission_classes = [IsAdminUser]

    def put(self, request, package_id):
        package = get_object_or_404(Package, id=package_id)
        serializer = PackageSerializer(package, data=request.data, partial=True)
        
        if serializer.is_valid():
            serializer.save()
            return Response(serializer.data, status=status.HTTP_200_OK)
        
        return Response({"error": serializer.errors}, status=status.HTTP_400_BAD_REQUEST)

class UserRequestCountView(APIView):
    permission_classes = [IsAuthenticated]

    def get(self, request):
        # Get user's quota and package
        quota = Quota.objects.filter(user=request.user).select_related('package').first()
        if not quota or not quota.package:
            return Response({
                'error': 'User has no active package'
            }, status=status.HTTP_400_BAD_REQUEST)

        # Count requests in the last 24 hours
        # yesterday = timezone.now() - timedelta(days=1)
        zero_hour = timezone.now().replace(hour=0, minute=0, second=0, microsecond=0)
        request_count = ChatMessage.objects.filter(
            conversation__workspace__user=request.user,
            type=LLM_MESSAGE,
            created_at__gte=zero_hour
        )

        return Response({
            'request_count': request_count.count(),
            'limit_request': quota.package.limit_request,
        }, status=status.HTTP_200_OK)
