from rest_framework.routers import DefaultRouter
from django.urls import include, path
from .views import *

router = DefaultRouter(trailing_slash=False)
router.register('documents', DocumentViewSet, basename='documents')
router.register('law_clauses', LawClauseViewSet, basename='law_clauses')
router.register('document-related', DocumentRelatedView, basename='document-related')
urlpatterns = [
    path('', include(router.urls)),
]
