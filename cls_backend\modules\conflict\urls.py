from django.urls import path, include
from rest_framework.routers import <PERSON>fault<PERSON>out<PERSON>

from .views import SearchEffectiveTimeView, CheckEffectiveTimeView, \
    AuthorityViewSet, AuthorityHistoryViewSet

router = DefaultRouter(trailing_slash=False)

router.register(r'files', AuthorityViewSet, basename='authorities')  # thẩm quyền
router.register(r'histories', AuthorityHistoryViewSet, basename='histories')  # thẩm quyền

urlpatterns = [
    path('', include(router.urls)),
    path('thoi_gian_hieu_luc/search', SearchEffectiveTimeView.as_view()),
    path('thoi_gian_hieu_luc/check', CheckEffectiveTimeView.as_view()),
]
