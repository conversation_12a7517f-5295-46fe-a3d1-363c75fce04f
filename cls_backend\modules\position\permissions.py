# permissions.py
from rest_framework.permissions import BasePermission
from organization.models import OrganizationMember
from organization.constants import *

class IsSuperAdminOrOrganizationAdmin(BasePermission):
    def has_object_permission(self, request, view, obj):
        user = request.user

        if user and user.is_authenticated and user.is_staff:
            return True

        return OrganizationMember.objects.filter(
            user=user,
            role=ADMIN,
            organization=obj.organization,
            is_active=True
        ).exists()
