from rest_framework import serializers
from ..models import OrganizationMember

class OrganizationMemberSerializer(serializers.ModelSerializer):
    user_email = serializers.CharField(source='user.email', read_only=True)
    user_fullname = serializers.CharField(source='user.fullname', read_only=True)
    organization_name = serializers.CharField(source='organization.name', read_only=True)

    class Meta:
        model = OrganizationMember
        fields = [
            'id',
            'organization',
            'organization_name',
            'user',
            'user_email',
            'user_fullname',
            'role',
            'is_active',
            'joined_at',
            'updated_at'
        ]
        read_only_fields = ['id', 'joined_at', 'updated_at', 'organization_name', 'user_email', 'user_fullname']