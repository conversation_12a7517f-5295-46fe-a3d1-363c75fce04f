from rest_framework import serializers
from chatbot.models import Conversation, ChatMessage
from cls_backend.models import WorkSpace



class ConversationSerializer(serializers.ModelSerializer):
    workspace_id = serializers.PrimaryKeyRelatedField(source='workspace', queryset=WorkSpace.objects.all())
    
    class Meta:
        model = Conversation
        fields = ['id', 'name', 'workspace_id', 'created_at', 'updated_at', 'deleted_at']
        read_only_fields = ['id', 'created_at', 'deleted_at']

class ChatMessageSerializer(serializers.ModelSerializer):
    answer = serializers.SerializerMethodField()
    role = serializers.SerializerMethodField()

    def get_answer(self, obj):
        return obj.text
    
    def get_role(self, obj):
        return obj.type

    class Meta:
        model = ChatMessage
        fields = ['id', 'answer', 'role','feedback']
        read_only_fields = ['id', 'created_at', 'deleted_at']
