from rest_framework import serializers

from cls_backend.models import User
from cls_backend.utils.file_utils import generate_presigned_url
from organization.models import OrganizationMember

class UserSerializer(serializers.ModelSerializer):
    password = serializers.CharField(write_only=True)
    # token = serializers.ReadOnlyField()

    class Meta:
        model = User
        fields = ['id', 'email', 'password', 'avatar', 'fullname', 'is_staff', 'phone', 'position', 'gender', 'dob', 'address']

    def create(self, validated_data):
        return User.objects.create_user(**validated_data)

    def to_representation(self, instance):
        data = super().to_representation(instance)
        # Ưu tiên role SUPER_ADMIN nếu là superuser
        if instance.is_superuser:
            data['role'] = 'SUPER_ADMIN'
        else:
            # Kiểm tra role admin trong OrganizationMember
            
            is_org_admin = OrganizationMember.objects.filter(user=instance, role='admin').exists()
            if is_org_admin or instance.is_staff:
                data['role'] = 'ADMIN'
            else:
                data['role'] = 'USER'
        data.pop('is_staff')

        # Tạo presigned URL cho avatar nếu có
        if instance.avatar:
            avatar_url = generate_presigned_url(instance.avatar.name, expiration=3600)
            data['avatar'] = avatar_url
        else:
            data['avatar'] = None

        return data


class ChangePasswordSerializer(serializers.Serializer):
    model = User

    """
    Serializer for password change endpoint.
    """
    current_password = serializers.CharField(required=True)
    new_password = serializers.CharField(required=True)
    
class PasswordResetSerializer(serializers.Serializer):
    email = serializers.EmailField(required=True)
    
class PasswordResetConfirmSerializer(serializers.Serializer):
    new_password = serializers.CharField(min_length=6, required=True)


class AdminPasswordResetSerializer(serializers.Serializer):
    """
    Serializer cho API reset password bởi admin/super user
    Chỉ cần user_id, mật khẩu mới sẽ được tự động generate
    """
    user_id = serializers.IntegerField(required=True)

    def validate_user_id(self, value):
        """Validate user_id tồn tại"""
        from cls_backend.models import User
        try:
            user = User.objects.get(id=value)
            if user.is_superuser:
                raise serializers.ValidationError("Không thể reset mật khẩu của super user khác.")
            return value
        except User.DoesNotExist:
            raise serializers.ValidationError("User không tồn tại.")


class UpdateUserProfileSerializer(serializers.ModelSerializer):
    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)
        # Kiểm tra các field không hợp lệ trong input data
        if self.initial_data is not None:
            allowed_fields = set(self.fields.keys())
            # Các trường không cho phép cập nhật
            forbidden_fields = {'password', 'email', 'position'}
            invalid_fields = set(self.initial_data.keys()) - allowed_fields
            errors = {}
            for field in forbidden_fields:
                if field in self.initial_data:
                    errors[field] = f'Không được phép thay đổi {field.replace("position", "chức vụ").replace("password", "mật khẩu").replace("email", "email") }.'
            if invalid_fields:
                for field in invalid_fields:
                    errors[field] = 'Trường này không hợp lệ.'
            if errors:
                from rest_framework.exceptions import ValidationError
                raise ValidationError(errors)
    """
    Serializer chuyên dụng cho việc cập nhật thông tin profile người dùng
    """
    email = serializers.EmailField(read_only=True)  # Email không được phép thay đổi
    position = serializers.CharField(read_only=True)

    class Meta:
        model = User
        fields = ['id', 'email', 'fullname', 'avatar', 'phone', 'position', 'gender', 'dob', 'address']
        extra_kwargs = {
            'id': {'read_only': True},      
            'position': {'read_only': True},
        }

    def validate_fullname(self, value):
        """Validate fullname field"""
        if not value or not value.strip():
            raise serializers.ValidationError("Họ tên không được để trống.")
        if len(value.strip()) < 2:
            raise serializers.ValidationError("Họ tên phải có ít nhất 2 ký tự.")
        if len(value.strip()) > 255:
            raise serializers.ValidationError("Họ tên không được vượt quá 255 ký tự.")
        return value.strip()

    def validate_avatar(self, value):
        """Validate avatar file"""
        if value:
            # Kiểm tra kích thước file (tối đa 5MB)
            if value.size > 5 * 1024 * 1024:
                raise serializers.ValidationError("Kích thước ảnh không được vượt quá 5MB.")

            # Kiểm tra định dạng file
            allowed_types = ['image/jpeg', 'image/jpg', 'image/png', 'image/gif']
            if hasattr(value, 'content_type') and value.content_type not in allowed_types:
                raise serializers.ValidationError("Chỉ chấp nhận file ảnh định dạng JPEG, PNG, GIF.")

        return value

    def validate(self, attrs):
        """Không cho phép cập nhật email, password,  position qua serializer này"""
        forbidden_fields = ['email', 'password', 'position']
        errors = {}
        for field in forbidden_fields:
            if field in attrs:
                errors[field] = f'Không được phép thay đổi {field.replace("position", "chức vụ").replace("password", "mật khẩu").replace("email", "email") }.'
        if errors:
            from rest_framework.exceptions import ValidationError
            raise ValidationError(errors)
        return attrs

    def update(self, instance, validated_data):
        """Chỉ cập nhật các trường cho phép"""
        for attr, value in validated_data.items():
            setattr(instance, attr, value)
        instance.save()
        return instance

    def to_representation(self, instance):
        """Customize response data"""
        data = super().to_representation(instance)

        # Thêm role field
        if instance.is_superuser:
            data['role'] = 'SUPER_ADMIN'
        else:
            # Kiểm tra role admin trong OrganizationMember
            
            is_org_admin = OrganizationMember.objects.filter(user=instance, role='admin').exists()
            if is_org_admin or instance.is_staff:
                data['role'] = 'ADMIN'
            else:
                data['role'] = 'USER'

        # Tạo presigned URL cho avatar nếu có
        if instance.avatar:
            avatar_url = generate_presigned_url(instance.avatar.name, expiration=3600)
            data['avatar'] = avatar_url
        else:
            data['avatar'] = None

        return data
